<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/province/list') ?>" class="text-light-text">Provinces</a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Create Province</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-plus"></i> Create New Province</h5>
                    <a href="<?= base_url('dakoii/province/list') ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to Provinces
                    </a>
                </div>
                <div class="card-body">
                    <?= form_open('dakoii/province/create', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                    
                    <input type="hidden" name="country_id" value="<?= $set_country['id'] ?>">

                    <div class="mb-3">
                        <label for="provincecode" class="form-label">Province Code <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control" 
                               id="provincecode" 
                               name="provincecode" 
                               value="<?= old('provincecode') ?>"
                               maxlength="10" 
                               required>
                        <div class="form-text">Enter a unique province code (max 10 characters)</div>
                        <div class="invalid-feedback">
                            Please provide a valid province code.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">Province Name <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control" 
                               id="name" 
                               name="name" 
                               value="<?= old('name') ?>"
                               maxlength="100" 
                               required>
                        <div class="form-text">Enter the full province name (max 100 characters)</div>
                        <div class="invalid-feedback">
                            Please provide a valid province name (minimum 3 characters).
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="country_id_display" class="form-label">Country</label>
                        <input type="text" 
                               class="form-control" 
                               id="country_id_display" 
                               value="<?= esc($set_country['name']) ?>" 
                               readonly>
                        <div class="form-text">Country is automatically set to <?= esc($set_country['name']) ?></div>
                    </div>

                    <div class="mb-3">
                        <label for="json_id" class="form-label">JSON ID</label>
                        <input type="text" 
                               class="form-control" 
                               id="json_id" 
                               name="json_id" 
                               value="<?= old('json_id') ?>"
                               maxlength="50">
                        <div class="form-text">Optional JSON identifier for mapping purposes</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('dakoii/province/list') ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Province
                        </button>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Information Panel -->
        <div class="col-md-4 col-lg-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Information</h5>
                </div>
                <div class="card-body">
                    <h6 class="fw-bold">Creating a New Province</h6>
                    <p class="text-muted">When creating a new province, please ensure:</p>
                    <ul class="text-muted">
                        <li>Province code is unique and follows your organization's naming convention</li>
                        <li>Province name is descriptive and accurate</li>
                        <li>JSON ID is used for external system integration (optional)</li>
                    </ul>
                    
                    <hr>
                    
                    <h6 class="fw-bold">Current Country</h6>
                    <p class="text-muted">
                        <i class="fas fa-flag me-2"></i>
                        <strong><?= esc($set_country['name']) ?></strong><br>
                        <small>All provinces will be created under this country</small>
                    </p>

                    <hr>

                    <h6 class="fw-bold">Next Steps</h6>
                    <p class="text-muted">After creating the province, you can:</p>
                    <ul class="text-muted">
                        <li>Add districts to the province</li>
                        <li>Edit province details if needed</li>
                        <li>View the province in the provinces list</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
