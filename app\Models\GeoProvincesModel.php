<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * GeoProvincesModel
 *
 * Model for the geo_provinces table
 */
class GeoProvincesModel extends Model
{
    protected $table = 'geo_provinces';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    protected $allowedFields = [
        'province_code',
        'name',
        'country_id',
        'json_id',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'province_code' => 'required|max_length[10]',
        'name' => 'required|max_length[100]',
        'country_id' => 'required|numeric',
        'json_id' => 'permit_empty|max_length[50]'
    ];

    protected $validationMessages = [
        'province_code' => [
            'required' => 'Province code is required',
            'max_length' => 'Province code cannot exceed 10 characters',
            'is_unique' => 'Province code already exists'
        ],
        'name' => [
            'required' => 'Province name is required',
            'max_length' => 'Province name cannot exceed 100 characters'
        ],
        'country_id' => [
            'required' => 'Country ID is required',
            'numeric' => 'Country ID must be a number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Before insert/update validation
     */
    protected function validateProvinceCode(array $data): bool
    {
        if (!isset($data['province_code'])) {
            return true; // Let required validation handle this
        }

        $builder = $this->db->table($this->table);
        $builder->where('province_code', $data['province_code']);

        // If updating, exclude current record
        if (isset($data['id'])) {
            $builder->where('id !=', $data['id']);
        }

        $existing = $builder->get()->getRow();

        if ($existing) {
            $this->errors = ['province_code' => 'Province code already exists'];
            return false;
        }

        return true;
    }

    /**
     * Override insert to add custom validation
     */
    public function insert($data = null, bool $returnID = true)
    {
        if (!$this->validateProvinceCode($data)) {
            return false;
        }

        return parent::insert($data, $returnID);
    }

    /**
     * Override update to add custom validation
     */
    public function update($id = null, $data = null): bool
    {
        // Add ID to data for validation
        if (is_array($data)) {
            $data['id'] = $id;
        }

        if (!$this->validateProvinceCode($data)) {
            return false;
        }

        // Remove ID from data before update
        if (is_array($data) && isset($data['id'])) {
            unset($data['id']);
        }

        return parent::update($id, $data);
    }

    /**
     * Get provinces by country ID
     *
     * @param int $countryId
     * @return array
     */
    public function getProvincesByCountry($countryId)
    {
        return $this->where('country_id', $countryId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get province by code
     *
     * @param string $code
     * @return array|null
     */
    public function getProvinceByCode($code)
    {
        return $this->where('province_code', $code)->first();
    }

    /**
     * Search provinces by name
     *
     * @param string $search
     * @param int $countryId
     * @return array
     */
    public function searchProvinces($search, $countryId = null)
    {
        $builder = $this->like('name', $search);

        if ($countryId !== null) {
            $builder->where('country_id', $countryId);
        }

        return $builder->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Get all provinces ordered by name
     *
     * @return array
     */
    public function getAllProvinces()
    {
        return $this->orderBy('name', 'ASC')->findAll();
    }
}
