<?php
/**
 * View file for listing positions for reports
 *
 * @var array $exercise Exercise details
 * @var array $positions List of positions in the exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Positions - <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Positions for Reports</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        Available Positions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No positions found</h5>
                            <p class="text-muted">No positions are available for this exercise.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Position Details</th>
                                        <th>Classification & Award</th>
                                        <th>Statistics</th>
                                        <th>Available Reports</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $position): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?= esc($position['designation']) ?></h6>
                                                    <small class="text-muted d-block">
                                                        <strong>Ref:</strong> <?= esc($position['position_reference']) ?>
                                                    </small>
                                                    <small class="text-muted d-block">
                                                        <strong>Group:</strong> <?= esc($position['group_name']) ?>
                                                    </small>
                                                    <small class="text-muted">
                                                        <strong>Location:</strong> <?= esc($position['location']) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="badge bg-secondary mb-1"><?= esc($position['classification']) ?></span>
                                                    <div class="small text-muted">
                                                        <strong>Award:</strong> <?= esc($position['award']) ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <small class="text-muted">
                                                        <i class="fas fa-users me-1"></i>
                                                        <?= $position['application_count'] ?> Applications
                                                    </small>
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        <?= $position['shortlisted_count'] ?> Shortlisted
                                                    </small>
                                                    <small class="text-info">
                                                        <i class="fas fa-comments me-1"></i>
                                                        <?= $position['interviewed_count'] ?> Interviewed
                                                    </small>
                                                    <small class="text-warning">
                                                        <i class="fas fa-trophy me-1"></i>
                                                        <?= $position['selected_count'] ?> Selected
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical" role="group">
                                                    <!-- Application Register -->
                                                    <a href="<?= base_url('reports/application-register/' . $position['id']) ?>" 
                                                       class="btn btn-outline-primary btn-sm mb-1" 
                                                       title="Application Register">
                                                        <i class="fas fa-file-alt me-1"></i>
                                                        Application Register
                                                    </a>
                                                    
                                                    <!-- Pre-Screening Report -->
                                                    <a href="<?= base_url('reports/pre-screening/' . $position['id']) ?>" 
                                                       class="btn btn-outline-success btn-sm mb-1" 
                                                       title="Pre-Screening Report">
                                                        <i class="fas fa-filter me-1"></i>
                                                        Pre-Screening
                                                    </a>
                                                    
                                                    <!-- Form 3.7 Profiling -->
                                                    <a href="<?= base_url('reports/profiling/' . $position['id']) ?>" 
                                                       class="btn btn-outline-info btn-sm mb-1" 
                                                       title="Form 3.7 Profiling Report">
                                                        <i class="fas fa-user-check me-1"></i>
                                                        Form 3.7 Profiling
                                                    </a>
                                                    
                                                    <!-- Form 3.7A Updated -->
                                                    <a href="<?= base_url('reports/profiling-updated/' . $position['id']) ?>" 
                                                       class="btn btn-outline-warning btn-sm mb-1" 
                                                       title="Form 3.7A Updated Report">
                                                        <i class="fas fa-user-edit me-1"></i>
                                                        Form 3.7A Updated
                                                    </a>
                                                    
                                                    <!-- Scoring Report -->
                                                    <a href="<?= base_url('reports/scoring/' . $position['id']) ?>" 
                                                       class="btn btn-outline-secondary btn-sm mb-1" 
                                                       title="Application Scoring Report">
                                                        <i class="fas fa-star me-1"></i>
                                                        Scoring Report
                                                    </a>
                                                    
                                                    <!-- Interview Scoring -->
                                                    <a href="<?= base_url('reports/interview-scoring/' . $position['id']) ?>" 
                                                       class="btn btn-outline-dark btn-sm" 
                                                       title="Interview Scoring Report">
                                                        <i class="fas fa-comments me-1"></i>
                                                        Interview Scoring
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Report Information
                    </h6>
                    <p class="card-text">
                        Select a position above to access various reports. Each report provides different insights:
                    </p>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>Application Register:</strong> Complete list of all applications</li>
                                <li><strong>Pre-Screening:</strong> Pass/fail status with remarks</li>
                                <li><strong>Form 3.7 Profiling:</strong> Detailed candidate profiles</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>Form 3.7A Updated:</strong> Profiles with scoring results</li>
                                <li><strong>Scoring Report:</strong> Application rating details</li>
                                <li><strong>Interview Scoring:</strong> Interview results and rankings</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
