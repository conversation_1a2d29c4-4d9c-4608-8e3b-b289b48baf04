<?php
/**
 * View file for listing all positions within an exercise for pre-screening
 *
 * @var array $exercise Exercise details
 * @var array $positions List of all positions in the exercise with their position groups
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercises') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-briefcase me-2"></i>Positions for Pre-Screening</h2>
            <p class="text-muted">
                Exercise: <strong><?= esc($exercise['exercise_name']) ?></strong><br>
                <small>Advertisement No: <?= esc($exercise['advertisement_no']) ?> | Gazetted No: <?= esc($exercise['gazzetted_no']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('application_pre_screening/exercises') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <!-- Exercise Details Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>Exercise Details
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Exercise Name:</strong><br>
                    <?= esc($exercise['exercise_name']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Advertisement No:</strong><br>
                    <?= esc($exercise['advertisement_no']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Publication Period:</strong><br>
                    <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> -
                    <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                </div>
                <div class="col-md-3">
                    <strong>Total Positions:</strong><br>
                    <span class="badge bg-primary fs-6"><?= count($positions) ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="card">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">
                <i class="fas fa-briefcase me-2"></i>Available Positions
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No positions found for this exercise.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="12%">Reference</th>
                                <th width="20%">Position Title</th>
                                <th width="12%">Group</th>
                                <th width="10%">Classification</th>
                                <th width="15%">Location</th>
                                <th width="8%">Apps</th>
                                <th width="8%">Pending</th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td>
                                        <code><?= esc($position['position_reference']) ?></code>
                                    </td>
                                    <td>
                                        <strong><?= esc($position['designation']) ?></strong><br>
                                        <small class="text-muted"><?= esc($position['award']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= esc($position['position_group_name']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= esc($position['classification']) ?></span>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?= esc($position['location']) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= $position['applications_count'] ?></span>
                                    </td>
                                    <td>
                                        <?php if ($position['pending_prescreen_count'] > 0): ?>
                                            <span class="badge bg-warning"><?= $position['pending_prescreen_count'] ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($position['applications_count'] > 0): ?>
                                            <a href="<?= base_url('application_pre_screening/position_applications/' . $position['id']) ?>"
                                               class="btn btn-sm btn-primary"
                                               data-bs-toggle="tooltip"
                                               title="View Applications">
                                                <i class="fas fa-list"></i>
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-sm btn-outline-secondary" disabled>
                                                <i class="fas fa-inbox"></i>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary Statistics -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-bar me-2"></i>Exercise Summary
                                </h6>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <h4 class="text-primary"><?= count($positions) ?></h4>
                                        <small class="text-muted">Total Positions</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-info"><?= array_sum(array_column($positions, 'applications_count')) ?></h4>
                                        <small class="text-muted">Total Applications</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-warning"><?= array_sum(array_column($positions, 'pending_prescreen_count')) ?></h4>
                                        <small class="text-muted">Pending Pre-Screening</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-success"><?= array_sum(array_column($positions, 'passed_prescreen_count')) ?></h4>
                                        <small class="text-muted">Passed Pre-Screening</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add global AJAX error handler for 401 unauthorized responses (session timeout)
    $(document).ajaxError(function(event, jqXHR, settings, thrownError) {
        if (jqXHR.status === 401) {
            try {
                const response = JSON.parse(jqXHR.responseText);
                if (response.redirect) {
                    toastr.error(response.message || 'Your session has expired. Please login again.');
                    setTimeout(function() {
                        window.location.href = response.redirect;
                    }, 2000); // Redirect after 2 seconds
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                toastr.error('Your session has expired. Please login again.');
                setTimeout(function() {
                    window.location.href = '<?= base_url() ?>';
                }, 2000);
            }
        }
    });

    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by row number
        language: {
            search: "Search positions:",
            lengthMenu: "Show _MENU_ positions per page",
            info: "Showing _START_ to _END_ of _TOTAL_ positions",
            emptyTable: "No positions available in this exercise",
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    console.log('Exercise Positions view loaded');
});
</script>
<?= $this->endSection() ?>