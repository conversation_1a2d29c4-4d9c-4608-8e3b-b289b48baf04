<?php

namespace App\Controllers;

use CodeIgniter\Controller;

/**
 * ReportsApplicationController
 * 
 * Controller for application-related reports including application register and pre-screening reports
 */
class ReportsApplicationController extends Controller
{
    public function __construct()
    {
        helper(['url', 'form']);
    }

    /**
     * [GET] Application Register Report
     * URI: /reports/application-register/{positionId}
     */
    public function applicationRegister($positionId)
    {
        // Mock position data
        $position = $this->getMockPosition($positionId);
        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Mock application register data based on AppxApplicationDetailsModel
        $applications = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'date_of_birth' => '1990-05-15',
                'place_of_origin' => 'Western Province',
                'contact_details' => '{"phone": "71234567", "email": "<EMAIL>"}',
                'location_address' => 'Port Moresby, NCD',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Single',
                'current_employer' => 'ABC Company Ltd',
                'current_position' => 'Software Developer',
                'current_salary' => 'K65,000',
                'application_status' => 'received',
                'received_at' => '2024-02-01 09:30:00',
                'created_at' => '2024-01-28 14:20:00'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Mary',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'date_of_birth' => '1988-08-22',
                'place_of_origin' => 'Morobe Province',
                'contact_details' => '{"phone": "72345678", "email": "<EMAIL>"}',
                'location_address' => 'Lae, Morobe',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Married',
                'current_employer' => 'XYZ Corporation',
                'current_position' => 'Systems Analyst',
                'current_salary' => 'K70,000',
                'application_status' => 'received',
                'received_at' => '2024-02-02 11:15:00',
                'created_at' => '2024-01-29 16:45:00'
            ],
            [
                'id' => 3,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Peter',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'date_of_birth' => '1992-03-10',
                'place_of_origin' => 'Central Province',
                'contact_details' => '{"phone": "73456789", "email": "<EMAIL>"}',
                'location_address' => 'Port Moresby, NCD',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Single',
                'current_employer' => 'Tech Solutions PNG',
                'current_position' => 'Junior Developer',
                'current_salary' => 'K55,000',
                'application_status' => 'received',
                'received_at' => '2024-02-03 08:45:00',
                'created_at' => '2024-01-30 10:30:00'
            ]
        ];

        $data = [
            'title' => 'Application Register Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'applications' => $applications
        ];

        return view('application_reports/appx_reports_application_register', $data);
    }

    /**
     * [GET] Pre-Screening Report
     * URI: /reports/pre-screening/{positionId}
     */
    public function preScreening($positionId)
    {
        // Mock position data
        $position = $this->getMockPosition($positionId);
        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Mock pre-screening data with pass/fail status and remarks
        $preScreeningResults = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'contact_details' => '{"phone": "71234567", "email": "<EMAIL>"}',
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 85,
                'pre_screening_remarks' => 'Meets all minimum qualifications. Strong technical background.',
                'screened_by' => 'HR Officer 1',
                'screened_at' => '2024-02-05 10:30:00',
                'qualifications_met' => 'Yes',
                'experience_met' => 'Yes',
                'documents_complete' => 'Yes'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Mary',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'contact_details' => '{"phone": "72345678", "email": "<EMAIL>"}',
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 92,
                'pre_screening_remarks' => 'Excellent qualifications and extensive experience. Highly recommended.',
                'screened_by' => 'HR Officer 2',
                'screened_at' => '2024-02-05 14:15:00',
                'qualifications_met' => 'Yes',
                'experience_met' => 'Yes',
                'documents_complete' => 'Yes'
            ],
            [
                'id' => 3,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Peter',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'contact_details' => '{"phone": "73456789", "email": "<EMAIL>"}',
                'pre_screening_status' => 'failed',
                'pre_screening_score' => 45,
                'pre_screening_remarks' => 'Does not meet minimum experience requirements. Missing key certifications.',
                'screened_by' => 'HR Officer 1',
                'screened_at' => '2024-02-06 09:20:00',
                'qualifications_met' => 'Partial',
                'experience_met' => 'No',
                'documents_complete' => 'Yes'
            ],
            [
                'id' => 4,
                'application_number' => 'APP-2024-004',
                'first_name' => 'Sarah',
                'last_name' => 'Wilson',
                'gender' => 'Female',
                'contact_details' => '{"phone": "74567890", "email": "<EMAIL>"}',
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 78,
                'pre_screening_remarks' => 'Meets minimum requirements. Good potential candidate.',
                'screened_by' => 'HR Officer 2',
                'screened_at' => '2024-02-06 11:45:00',
                'qualifications_met' => 'Yes',
                'experience_met' => 'Yes',
                'documents_complete' => 'Yes'
            ]
        ];

        $data = [
            'title' => 'Pre-Screening Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'pre_screening_results' => $preScreeningResults
        ];

        return view('application_reports/appx_reports_pre_screening', $data);
    }

    /**
     * [POST] Export Application Register Report
     * URI: /reports/application-register/export
     */
    public function exportApplicationRegister()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Application register report exported successfully',
            'file_url' => base_url('exports/application_register_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * [POST] Export Pre-Screening Report
     * URI: /reports/pre-screening/export
     */
    public function exportPreScreening()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Pre-screening report exported successfully',
            'file_url' => base_url('exports/pre_screening_report_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Get mock position data
     */
    private function getMockPosition($positionId)
    {
        $positions = [
            1 => [
                'id' => 1,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Engineer',
                'classification' => 'Grade 12',
                'award' => 'K85,000 - K95,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            2 => [
                'id' => 2,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'classification' => 'Grade 11',
                'award' => 'K75,000 - K85,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            3 => [
                'id' => 3,
                'exercise_id' => 1,
                'position_group_id' => 2,
                'position_reference' => 'SYS-001',
                'designation' => 'System Administrator',
                'classification' => 'Grade 10',
                'award' => 'K65,000 - K75,000',
                'location' => 'Port Moresby',
                'group_name' => 'System Administration',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ]
        ];

        return $positions[$positionId] ?? null;
    }
}
