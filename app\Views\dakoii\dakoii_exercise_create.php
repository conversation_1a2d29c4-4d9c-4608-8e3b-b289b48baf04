<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="text-light-text"><?= esc($org['name']) ?></a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Create Exercise</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-light-text mb-1">Create Exercise</h2>
            <p class="text-secondary mb-0">Create a new recruitment exercise for <?= esc($org['name']) ?></p>
        </div>
        <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
            <i class="fas fa-arrow-left me-1"></i> Back to Organization
        </a>
    </div>

    <!-- Exercise Creation Form -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <h5 class="fw-bold mb-0 text-white"><i class="fas fa-plus me-2"></i>Exercise Information</h5>
                </div>
                <div class="card-body p-4">
                    <form id="exerciseForm" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="org_id" value="<?= $org['id'] ?>">
                        <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">

                        <div class="row">
                            <!-- Exercise Name -->
                            <div class="col-md-6 mb-3">
                                <label for="exercise_name" class="form-label text-light-text">Exercise Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="exercise_name" name="exercise_name" required>
                                <div class="form-text text-secondary">Enter the name of the recruitment exercise</div>
                            </div>

                            <!-- Gazzetted Number -->
                            <div class="col-md-6 mb-3">
                                <label for="gazzetted_no" class="form-label text-light-text">Gazzetted Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="gazzetted_no" name="gazzetted_no" required>
                                <div class="form-text text-secondary">Official gazette reference number</div>
                            </div>

                            <!-- Gazzetted Date -->
                            <div class="col-md-6 mb-3">
                                <label for="gazzetted_date" class="form-label text-light-text">Gazzetted Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="gazzetted_date" name="gazzetted_date" required>
                            </div>

                            <!-- Advertisement Number -->
                            <div class="col-md-6 mb-3">
                                <label for="advertisement_no" class="form-label text-light-text">Advertisement Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="advertisement_no" name="advertisement_no" required>
                                <div class="form-text text-secondary">Advertisement reference number</div>
                            </div>

                            <!-- Advertisement Date -->
                            <div class="col-md-6 mb-3">
                                <label for="advertisement_date" class="form-label text-light-text">Advertisement Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="advertisement_date" name="advertisement_date" required>
                            </div>

                            <!-- Mode of Advertisement -->
                            <div class="col-md-6 mb-3">
                                <label for="mode_of_advertisement" class="form-label text-light-text">Mode of Advertisement <span class="text-danger">*</span></label>
                                <select class="form-select" id="mode_of_advertisement" name="mode_of_advertisement" required>
                                    <option value="">Select Mode</option>
                                    <option value="Online">Online</option>
                                    <option value="Print">Print Media</option>
                                    <option value="Print & Online">Print & Online</option>
                                    <option value="Radio">Radio</option>
                                    <option value="Television">Television</option>
                                    <option value="Multiple Media">Multiple Media</option>
                                </select>
                            </div>

                            <!-- Publish Date From -->
                            <div class="col-md-6 mb-3">
                                <label for="publish_date_from" class="form-label text-light-text">Publish Date From <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="publish_date_from" name="publish_date_from" required>
                            </div>

                            <!-- Publish Date To -->
                            <div class="col-md-6 mb-3">
                                <label for="publish_date_to" class="form-label text-light-text">Publish Date To <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="publish_date_to" name="publish_date_to" required>
                            </div>

                            <!-- Description -->
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label text-light-text">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter exercise description (optional)"></textarea>
                                <div class="form-text text-secondary">Provide additional details about the exercise</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary text-white">
                                <i class="fas fa-save me-1"></i> Create Exercise
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('exerciseForm');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(form);
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Creating...';
        submitBtn.disabled = true;
        
        // Submit to ExercisesController API
        fetch('<?= base_url('exercises/create') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message and redirect
                alert('Exercise created successfully!');
                window.location.href = '<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>';
            } else {
                alert('Error: ' + (data.message || 'Failed to create exercise'));
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the exercise');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>

<?= $this->endSection() ?>
