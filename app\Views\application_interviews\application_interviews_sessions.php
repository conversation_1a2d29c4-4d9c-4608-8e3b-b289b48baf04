<?php
/**
 * Interview Sessions Management
 * Shows interview sessions for a specific exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Sessions</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-clipboard-list me-1"></i>
                        <?= esc($exercise['exercise_name']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Exercises
                    </a>
                    <a href="<?= base_url('interviews/sessions/create/' . $exercise['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>New Interview Session
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Info Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-clipboard-list text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Exercise Status</h6>
                        <span class="badge bg-info text-dark"><?= ucfirst($exercise['status']) ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-calendar text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Duration</h6>
                        <small class="text-muted">
                            <?= date('M d, Y', strtotime($exercise['publish_date_from'])) ?><br>
                            to <?= date('M d, Y', strtotime($exercise['publish_date_to'])) ?>
                        </small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-users text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Interview Sessions</h6>
                        <span class="fw-bold"><?= count($sessions) ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-file-alt text-info mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Advertisement</h6>
                        <small class="text-muted"><?= esc($exercise['advertisement_no']) ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interview Sessions Card -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        Interview Sessions
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-secondary">
                        <?= count($sessions) ?> Session(s)
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($sessions)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-calendar-alt text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Interview Sessions</h5>
                    <p class="text-muted">Create your first interview session to start managing interviews for this exercise.</p>
                    <a href="<?= base_url('interviews/sessions/create/' . $exercise['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Interview Session
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Session Details</th>
                                <th>Duration</th>
                                <th>Status</th>
                                <th>Positions</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sessions as $session): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?= esc($session['session_name']) ?></h6>
                                            <small class="text-muted">
                                                <?= esc($session['session_description']) ?>
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                Created: <?= date('M d, Y', strtotime($session['created_at'])) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium">
                                                <?= date('M d, Y', strtotime($session['start_date'])) ?>
                                            </div>
                                            <small class="text-muted">to</small>
                                            <div class="fw-medium">
                                                <?= date('M d, Y', strtotime($session['end_date'])) ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge <?= $session['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?>">
                                            <?= ucfirst($session['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-dark">
                                            <?= $session['positions_count'] ?> Position(s)
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('interviews/session/' . $session['id'] . '/positions') ?>"
                                               class="btn btn-primary btn-sm"
                                               title="Manage Positions">
                                                <i class="fas fa-briefcase"></i>
                                            </a>
                                            <a href="<?= base_url('interviews/session/' . $session['id'] . '/interviewers') ?>"
                                               class="btn btn-outline-primary btn-sm"
                                               title="Manage Interviewers">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <a href="<?= base_url('interviews/session/' . $session['id'] . '/settings') ?>"
                                               class="btn btn-outline-secondary btn-sm"
                                               title="Session Settings">
                                                <i class="fas fa-cog"></i>
                                            </a>
                                            <a href="<?= base_url('interviews/session/' . $session['id'] . '/schedule') ?>"
                                               class="btn btn-outline-info btn-sm"
                                               title="Interview Schedule">
                                                <i class="fas fa-calendar"></i>
                                            </a>
                                            <a href="<?= base_url('interviews/sessions/edit/' . $session['id']) ?>"
                                               class="btn btn-outline-warning btn-sm"
                                               title="Edit Session">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-outline-danger btn-sm delete-session-btn"
                                                    data-session-id="<?= $session['id'] ?>"
                                                    data-session-name="<?= esc($session['session_name']) ?>"
                                                    title="Delete Session">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle delete session
    document.querySelectorAll('.delete-session-btn').forEach(button => {
        button.addEventListener('click', function() {
            const sessionId = this.getAttribute('data-session-id');
            const sessionName = this.getAttribute('data-session-name');

            if (confirm(`Are you sure you want to delete the interview session "${sessionName}"? This action cannot be undone.`)) {
                // Mock delete action for UI development
                fetch(`<?= base_url('interviews/sessions/delete/') ?>${sessionId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        if (typeof toastr !== 'undefined') {
                            toastr.success(data.message);
                        } else {
                            alert(data.message);
                        }
                        // Reload page to reflect changes
                        window.location.reload();
                    } else {
                        if (typeof toastr !== 'undefined') {
                            toastr.error(data.message);
                        } else {
                            alert('Error: ' + data.message);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (typeof toastr !== 'undefined') {
                        toastr.error('An error occurred while deleting the session');
                    } else {
                        alert('An error occurred while deleting the session');
                    }
                });
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
