<?php

namespace App\Controllers;

class HomeAuthController extends BaseController
{
    protected $session;
    protected $usersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->usersModel = new \App\Models\UsersModel();
    }

    // Admin Login Methods
    public function loginForm()
    {
        return view('home/home_login', [
            'title' => 'Admin Login',
            'menu' => 'login'
        ]);
    }

    public function processLogin()
    {
        // Validate input
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('error', 'Username and password are required');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        try {
            // Find user by username
            $user = $this->usersModel->findByUsername($username);

            if ($user && password_verify($password, $user['password'])) {
                // Check if user is active
                if ($user['status'] !== '1') {
                    return redirect()->back()->with('error', 'Your account is inactive. Please contact administrator.');
                }

                // Check if user has admin role
                if (!in_array($user['role'], ['admin', 'supervisor'])) {
                    return redirect()->back()->with('error', 'Access denied. Admin privileges required.');
                }

                // Set session data
                $this->session->set([
                    'logged_in' => true,
                    'user_id' => $user['id'],
                    'name' => $user['name'],
                    'username' => $user['username'],
                    'role' => $user['role'],
                    'org_id' => $user['org_id'],
                    'orgcode' => $user['orgcode'],
                    'position' => $user['position'],
                    'email' => $user['email']
                ]);

                return redirect()->to('dashboard')->with('success', "Welcome back, {$user['name']}! You've successfully logged in.");
            } else {
                return redirect()->back()->with('error', 'Invalid username or password. Please try again.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Login error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred during login. Please try again.');
        }
    }

    // Admin Logout Method
    public function adminLogout()
    {
        // Destroy the admin's session
        $this->session->destroy();

        return redirect()->to('login')->with('success', 'You have been logged out successfully.');
    }

    // Applicant Registration Methods
    public function registerForm()
    {
        return view('home/home_register', [
            'title' => 'Create Account',
            'menu' => 'register'
        ]);
    }

    public function processRegister()
    {
        $rules = [
            'firstname' => 'required|min_length[2]|max_length[50]',
            'lastname' => 'required|min_length[2]|max_length[50]',
            'email' => 'required|valid_email',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Mock registration for UI development - just show success
        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Account Created');
        session()->setFlashdata('swal_text', 'Your account has been created successfully. Please check your email for activation instructions.');

        return redirect()->to('/');
    }

    // Applicant Login Methods
    public function applicantLoginForm()
    {
        return view('home/home_applicant_login', [
            'title' => 'Applicant Login',
            'menu' => 'applicant_login'
        ]);
    }

    public function processApplicantLogin()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        // Validate input
        if (!$email || !$password) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Missing Information');
            session()->setFlashdata('swal_text', 'Please enter both email and password.');
            return redirect()->back();
        }

        // Load the ApplicantsModel
        $applicantsModel = new \App\Models\ApplicantsModel();

        // Verify applicant credentials
        $applicant = $applicantsModel->verifyApplicant($email, $password);

        if (!$applicant) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Login Failed');
            session()->setFlashdata('swal_text', 'Invalid email or password. Please try again.');
            return redirect()->back();
        }

        // Check if account is activated (status = 1 means active)
        if ($applicant['status'] != 1) {
            session()->setFlashdata('swal_icon', 'warning');
            session()->setFlashdata('swal_title', 'Account Not Activated');
            session()->setFlashdata('swal_text', 'Your account is not yet activated. Please check your email for activation instructions.');
            return redirect()->back();
        }

        // Set session data with real applicant information
        $this->session->set([
            'logged_in' => true,
            'applicant_id' => $applicant['id'],
            'applicant_unique_id' => $applicant['unique_id'],
            'applicant_name' => trim($applicant['first_name'] . ' ' . $applicant['last_name']),
            'applicant_email' => $applicant['email'],
            'applicant_first_name' => $applicant['first_name'],
            'applicant_last_name' => $applicant['last_name']
        ]);

        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Login Successful');
        session()->setFlashdata('swal_text', 'Welcome back, ' . $applicant['first_name'] . '!');
        return redirect()->to('applicant/dashboard');
    }

    // Account Activation
    public function activate($token)
    {
        // Load the ApplicantsModel
        $applicantsModel = new \App\Models\ApplicantsModel();

        // Attempt to activate the account
        $activated = $applicantsModel->activateAccount($token);

        if ($activated) {
            session()->setFlashdata('swal_icon', 'success');
            session()->setFlashdata('swal_title', 'Account Activated');
            session()->setFlashdata('swal_text', 'Your account has been activated successfully. You can now login.');
        } else {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Activation Failed');
            session()->setFlashdata('swal_text', 'Invalid or expired activation token. Please contact support if you continue to have issues.');
        }

        return redirect()->to('applicant/login');
    }

    // Applicant Logout
    public function applicantLogout()
    {
        // Destroy the applicant's session
        $this->session->destroy();

        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Logged Out');
        session()->setFlashdata('swal_text', 'You have been logged out successfully.');

        return redirect()->to('applicant/login');
    }
}
