<?php
/**
 * View file for listing positions within an exercise for shortlisting
 *
 * @var array $exercise Exercise details
 * @var array $positions List of positions in the exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting') ?>">Shortlisting</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-briefcase me-2"></i>Positions for Shortlisting</h2>
            <p class="text-muted">
                <strong><?= esc($exercise['exercise_name']) ?></strong><br>
                Advertisement: <?= esc($exercise['advertisement_no']) ?> | 
                Gazzetted: <?= esc($exercise['gazzetted_no']) ?>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('shortlisting') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No positions found for this exercise.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="25%">Position</th>
                                <th width="15%">Reference</th>
                                <th width="15%">Classification</th>
                                <th width="15%">Location</th>
                                <th width="10%">Applications</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td>
                                        <strong><?= esc($position['designation']) ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            Group: <?= esc($position['position_group_name']) ?>
                                        </small>
                                    </td>
                                    <td><?= esc($position['reference']) ?></td>
                                    <td>
                                        <?= esc($position['classification']) ?>
                                        <br>
                                        <small class="text-success">K<?= number_format($position['annual_salary']) ?></small>
                                    </td>
                                    <td><?= esc($position['location']) ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?= $position['total_applications'] ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-users me-1"></i> View Applications
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        order: [[1, 'asc']], // Sort by position name
        language: {
            search: "Search positions:",
            lengthMenu: "Show _MENU_ positions per page",
            info: "Showing _START_ to _END_ of _TOTAL_ positions",
            emptyTable: "No positions found for this exercise",
        }
    });
});
</script>
<?= $this->endSection() ?>
