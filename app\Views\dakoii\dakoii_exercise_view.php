<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <?php if (isset($exercise['orgcode'])): ?>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/view/' . $exercise['orgcode']) ?>" class="text-light-text"><?= esc($exercise['org_name'] ?? 'Organization') ?></a></li>
            <?php endif; ?>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Exercise Details</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-light-text mb-1"><?= esc($exercise['exercise_name']) ?></h2>
            <p class="text-secondary mb-0">Exercise Details and Information</p>
        </div>
        <div class="d-flex gap-2">
            <?php if (isset($exercise['orgcode'])): ?>
            <a href="<?= base_url('dakoii/organization/view/' . $exercise['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
                <i class="fas fa-arrow-left me-1"></i> Back to Organization
            </a>
            <?php endif; ?>
            <a href="<?= base_url('dakoii/organization/exercise/edit/' . $exercise['id']) ?>" class="btn btn-warning text-white">
                <i class="fas fa-edit me-1"></i> Edit Exercise
            </a>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary py-3">
                    <h5 class="fw-bold mb-0 text-white"><i class="fas fa-info-circle me-2"></i>Exercise Information</h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Exercise Name</label>
                            <p class="text-light-text fw-medium"><?= esc($exercise['exercise_name']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Status</label>
                            <div>
                                <?php
                                $statusClass = 'secondary';
                                $statusIcon = 'circle';
                                switch($exercise['status']) {
                                    case 'draft':
                                        $statusClass = 'secondary';
                                        $statusIcon = 'pencil-alt';
                                        break;
                                    case 'publish':
                                        $statusClass = 'success';
                                        $statusIcon = 'check-circle';
                                        break;
                                    case 'publish_request':
                                        $statusClass = 'warning';
                                        $statusIcon = 'clock';
                                        break;
                                    case 'selection':
                                        $statusClass = 'info';
                                        $statusIcon = 'tasks';
                                        break;
                                    case 'review':
                                        $statusClass = 'primary';
                                        $statusIcon = 'search';
                                        break;
                                    case 'closed':
                                        $statusClass = 'danger';
                                        $statusIcon = 'times-circle';
                                        break;
                                }
                                $textColor = ($statusClass == 'warning' || $statusClass == 'light') ? 'text-dark' : 'text-white';
                                ?>
                                <span class="badge bg-<?= $statusClass ?> <?= $textColor ?> px-3 py-2">
                                    <i class="fas fa-<?= $statusIcon ?> me-1"></i>
                                    <?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Gazzetted Number</label>
                            <p class="text-light-text fw-medium"><?= esc($exercise['gazzetted_no']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Gazzetted Date</label>
                            <p class="text-light-text fw-medium"><?= date('d M Y', strtotime($exercise['gazzetted_date'])) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Advertisement Number</label>
                            <p class="text-light-text fw-medium"><?= esc($exercise['advertisement_no']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Advertisement Date</label>
                            <p class="text-light-text fw-medium"><?= date('d M Y', strtotime($exercise['advertisement_date'])) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Mode of Advertisement</label>
                            <p class="text-light-text fw-medium"><?= esc($exercise['mode_of_advertisement']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-secondary">Publication Period</label>
                            <p class="text-light-text fw-medium">
                                <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> - 
                                <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                            </p>
                        </div>
                        <?php if (!empty($exercise['description'])): ?>
                        <div class="col-12 mb-3">
                            <label class="form-label text-secondary">Description</label>
                            <p class="text-light-text"><?= nl2br(esc($exercise['description'])) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Status Management -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-info py-3">
                    <h5 class="fw-bold mb-0 text-white"><i class="fas fa-cog me-2"></i>Status Management</h5>
                </div>
                <div class="card-body p-4">
                    <p class="text-secondary mb-3">Change the exercise status to manage its lifecycle.</p>
                    <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#changeStatusModal">
                        <i class="fas fa-edit me-1"></i> Change Status
                    </button>
                </div>
            </div>

            <!-- Organization Info -->
            <?php if (isset($exercise['org_name'])): ?>
            <div class="card shadow-sm border-0">
                <div class="card-header bg-secondary py-3">
                    <h5 class="fw-bold mb-0 text-white"><i class="fas fa-building me-2"></i>Organization</h5>
                </div>
                <div class="card-body p-4">
                    <p class="text-light-text fw-medium mb-2"><?= esc($exercise['org_name']) ?></p>
                    <?php if (isset($exercise['orgcode'])): ?>
                    <a href="<?= base_url('dakoii/organization/view/' . $exercise['orgcode']) ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i> View Organization
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div class="modal fade" id="changeStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-bg">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white">
                    <i class="fas fa-edit me-2"></i> Change Exercise Status
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('dakoii/organization/exercise/change-status/' . $exercise['id'] . '?redirect=view/' . $exercise['id']) ?>
            <div class="modal-body p-4">
                <?= csrf_field() ?>
                
                <div class="text-center mb-4">
                    <div class="d-inline-block p-3 rounded-circle bg-primary bg-opacity-25 mb-3">
                        <i class="fas fa-clipboard-list text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="text-light-text"><?= esc($exercise['exercise_name']) ?></h5>
                    <p class="text-secondary">Current Status: 
                        <span class="badge bg-<?= $statusClass ?> <?= $textColor ?>">
                            <?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?>
                        </span>
                    </p>
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label text-light-text">New Status</label>
                    <select class="form-select form-select-lg" name="status" id="status" required>
                        <option value="draft" <?= $exercise['status'] == 'draft' ? 'selected' : '' ?>>Draft</option>
                        <option value="publish" <?= $exercise['status'] == 'publish' ? 'selected' : '' ?>>Publish</option>
                        <option value="publish_request" <?= $exercise['status'] == 'publish_request' ? 'selected' : '' ?>>Publish Request</option>
                        <option value="selection" <?= $exercise['status'] == 'selection' ? 'selected' : '' ?>>Selection</option>
                        <option value="review" <?= $exercise['status'] == 'review' ? 'selected' : '' ?>>Review</option>
                        <option value="closed" <?= $exercise['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary text-white">
                    <i class="fas fa-save me-1"></i> Update Status
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
