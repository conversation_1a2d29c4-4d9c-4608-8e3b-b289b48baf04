<?php
/**
 * View file for Interview Scoring Report
 *
 * @var array $position Position details
 * @var array $interview_scoring_data List of interview scoring data
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Interview Scoring - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Interview Scoring Report</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?> - <?= esc($position['position_reference']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>
                        Export Excel
                    </button>
                    <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Group:</dt>
                                <dd class="col-sm-8"><?= esc($position['group_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interview Scoring Data -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments me-2"></i>
                        Interview Scoring Results (<?= count($interview_scoring_data) ?> Interviews)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($interview_scoring_data)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No interview scoring data found</h5>
                            <p class="text-muted">No interview scoring data is available for this position.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($interview_scoring_data as $index => $interview): ?>
                            <div class="card mb-4 border-secondary">
                                <div class="card-header bg-light">
                                    <div class="row align-items-center">
                                        <div class="col-md-4">
                                            <h6 class="mb-0">
                                                <strong><?= esc($interview['first_name'] . ' ' . $interview['last_name']) ?></strong>
                                                <span class="text-muted ms-2">(<?= esc($interview['application_number']) ?>)</span>
                                            </h6>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <span class="badge bg-primary fs-6">
                                                    Rank #<?= $interview['final_ranking'] ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="mb-0 text-success"><?= $interview['combined_score'] ?>%</h5>
                                                <small class="text-muted">Combined Score</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <span class="badge bg-success fs-6">
                                                <i class="fas fa-check me-1"></i>
                                                <?= ucfirst($interview['interview_status']) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Interview Information -->
                                        <div class="col-md-4">
                                            <h6 class="text-primary mb-3">Interview Information</h6>
                                            <?php $contact = json_decode($interview['contact_details'], true); ?>
                                            <dl class="row">
                                                <dt class="col-sm-5">Gender:</dt>
                                                <dd class="col-sm-7"><?= esc($interview['gender']) ?></dd>
                                                <dt class="col-sm-5">Phone:</dt>
                                                <dd class="col-sm-7"><?= esc($contact['phone'] ?? 'N/A') ?></dd>
                                                <dt class="col-sm-5">Email:</dt>
                                                <dd class="col-sm-7"><?= esc($contact['email'] ?? 'N/A') ?></dd>
                                                <dt class="col-sm-5">Date:</dt>
                                                <dd class="col-sm-7"><?= date('M d, Y', strtotime($interview['interview_date'])) ?></dd>
                                                <dt class="col-sm-5">Time:</dt>
                                                <dd class="col-sm-7"><?= $interview['interview_time'] ?></dd>
                                                <dt class="col-sm-5">Panel:</dt>
                                                <dd class="col-sm-7"><?= esc($interview['interview_panel']) ?></dd>
                                            </dl>
                                        </div>

                                        <!-- Interview Scoring Details -->
                                        <div class="col-md-8">
                                            <h6 class="text-success mb-3">Interview Scoring Breakdown</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>Criteria</th>
                                                            <th>Max Score</th>
                                                            <th>Score Achieved</th>
                                                            <th>Percentage</th>
                                                            <th>Progress</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($interview['interview_scores'] as $score): ?>
                                                            <tr>
                                                                <td><?= esc($score['criteria']) ?></td>
                                                                <td class="text-center"><?= $score['max_score'] ?></td>
                                                                <td class="text-center"><strong><?= $score['score'] ?></strong></td>
                                                                <td class="text-center"><?= number_format($score['percentage'], 1) ?>%</td>
                                                                <td>
                                                                    <div class="progress" style="height: 20px;">
                                                                        <div class="progress-bar bg-<?= $score['percentage'] >= 80 ? 'success' : ($score['percentage'] >= 60 ? 'warning' : 'danger') ?>" 
                                                                             role="progressbar" 
                                                                             style="width: <?= $score['percentage'] ?>%"
                                                                             aria-valuenow="<?= $score['percentage'] ?>" 
                                                                             aria-valuemin="0" 
                                                                             aria-valuemax="100">
                                                                            <?= number_format($score['percentage'], 1) ?>%
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                    <tfoot class="table-dark">
                                                        <tr>
                                                            <th>Interview Total</th>
                                                            <th class="text-center"><?= $interview['max_interview_score'] ?></th>
                                                            <th class="text-center"><strong><?= $interview['total_interview_score'] ?></strong></th>
                                                            <th class="text-center"><strong><?= $interview['interview_percentage'] ?>%</strong></th>
                                                            <th>
                                                                <div class="progress" style="height: 20px;">
                                                                    <div class="progress-bar bg-success" 
                                                                         role="progressbar" 
                                                                         style="width: <?= $interview['interview_percentage'] ?>%"
                                                                         aria-valuenow="<?= $interview['interview_percentage'] ?>" 
                                                                         aria-valuemin="0" 
                                                                         aria-valuemax="100">
                                                                        <?= $interview['interview_percentage'] ?>%
                                                                    </div>
                                                                </div>
                                                            </th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>

                                    <!-- Combined Scores -->
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h6 class="text-info mb-3">Combined Score Summary</h6>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded">
                                                        <h5 class="text-primary mb-1"><?= $interview['application_score'] ?>%</h5>
                                                        <small class="text-muted">Application Score</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded">
                                                        <h5 class="text-success mb-1"><?= $interview['interview_percentage'] ?>%</h5>
                                                        <small class="text-muted">Interview Score</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded bg-light">
                                                        <h5 class="text-warning mb-1"><?= $interview['combined_score'] ?>%</h5>
                                                        <small class="text-muted">Combined Score</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded bg-primary text-white">
                                                        <h5 class="mb-1">#<?= $interview['final_ranking'] ?></h5>
                                                        <small>Final Ranking</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (!empty($interview['interviewer_remarks'])): ?>
                                        <hr>
                                        <div class="row">
                                            <div class="col-12">
                                                <h6 class="text-info mb-2">Interview Panel Remarks</h6>
                                                <div class="alert alert-info">
                                                    <i class="fas fa-comment me-2"></i>
                                                    <?= esc($interview['interviewer_remarks']) ?>
                                                </div>
                                                <small class="text-muted">
                                                    <strong>Interviewed by:</strong> <?= esc($interview['interviewed_by']) ?> 
                                                    on <?= date('M d, Y \a\t H:i', strtotime($interview['interviewed_at'])) ?>
                                                </small>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Interview Scoring Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($interview_scoring_data) ?></h4>
                                <p class="mb-0">Total Interviewed</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php 
                                $avgInterviewScore = count($interview_scoring_data) > 0 ? 
                                    round(array_sum(array_column($interview_scoring_data, 'interview_percentage')) / count($interview_scoring_data), 1) : 0;
                                ?>
                                <h4 class="text-success"><?= $avgInterviewScore ?>%</h4>
                                <p class="mb-0">Avg. Interview Score</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php 
                                $avgCombinedScore = count($interview_scoring_data) > 0 ? 
                                    round(array_sum(array_column($interview_scoring_data, 'combined_score')) / count($interview_scoring_data), 1) : 0;
                                ?>
                                <h4 class="text-warning"><?= $avgCombinedScore ?>%</h4>
                                <p class="mb-0">Avg. Combined Score</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php 
                                $highScorers = count(array_filter($interview_scoring_data, function($interview) { return $interview['combined_score'] >= 80; }));
                                ?>
                                <h4 class="text-info"><?= $highScorers ?></h4>
                                <p class="mb-0">High Performers (≥80%)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // Mock export functionality for UI development
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show success message
        alert('Interview scoring report exported successfully!\n\nFile: interview_scoring_' + new Date().toISOString().slice(0,10) + '.xlsx');
    }, 2000);
}
</script>
<?= $this->endSection() ?>
