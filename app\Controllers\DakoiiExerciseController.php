<?php

namespace App\Controllers;

class DakoiiExerciseController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    /**
     * Change the status of an exercise
     *
     * @param int $exerciseId The ID of the exercise to update
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function exerciseChangeStatus($exerciseId)
    {
        $status = $this->request->getVar('status');

        // Validate the status
        $validStatuses = ['publish', 'draft', 'selection', 'review', 'publish_request', 'closed'];
        if (!in_array($status, $validStatuses)) {
            session()->setFlashdata('error', 'Invalid status value');
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        // Load the ExerciseModel
        $exerciseModel = new \App\Models\ExerciseModel();

        // Get the exercise
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        // Update the exercise status
        $exerciseModel->update($exerciseId, [
            'status' => $status,
            'updated_by' => session()->get('user_id')
        ]);

        session()->setFlashdata('success', 'Exercise status updated successfully to ' . ucfirst(str_replace('_', ' ', $status)));

        // Check if there's a redirect parameter
        $redirect = $this->request->getGet('redirect');
        if ($redirect && strpos($redirect, 'org/') === 0) {
            $orgcode = substr($redirect, 4);
            return redirect()->to(base_url('dakoii/organization/view/' . $orgcode));
        }

        return redirect()->to(base_url('dakoii/dashboard'));
    }

    /**
     * List all exercises with filtering options
     */
    public function exerciseList()
    {
        $exerciseModel = new \App\Models\ExerciseModel();

        $data['title'] = "Exercise Management";
        $data['menu'] = "exercises";

        // Get all exercises with organization information
        $exercises = $exerciseModel->findAll();

        // Add organization names to exercises
        foreach ($exercises as &$exercise) {
            if (!empty($exercise['org_id'])) {
                $org = $this->dakoiiOrgModel->find($exercise['org_id']);
                if ($org) {
                    $exercise['org_name'] = $this->dakoiiOrgModel->getOrganizationName($org);
                }
            }
        }

        $data['exercises'] = $exercises;
        echo view('dakoii/dakoii_exercises', $data);
    }

    /**
     * View exercise details
     */
    public function exerciseView($exerciseId)
    {
        $exerciseModel = new \App\Models\ExerciseModel();
        $exercise = $exerciseModel->find($exerciseId);

        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to('dakoii/exercises');
        }

        // Get organization information
        if (!empty($exercise['org_id'])) {
            $org = $this->dakoiiOrgModel->find($exercise['org_id']);
            if ($org) {
                $exercise['org_name'] = $this->dakoiiOrgModel->getOrganizationName($org);
                $exercise['organization'] = $org;
            }
        }

        $data['title'] = "Exercise Details";
        $data['menu'] = "exercises";
        $data['exercise'] = $exercise;

        echo view('dakoii/dakoii_exercise_view', $data);
    }
}
