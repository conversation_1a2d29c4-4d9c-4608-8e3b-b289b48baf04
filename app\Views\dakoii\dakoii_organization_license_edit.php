<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="text-light-text"><?= esc($org['name']) ?></a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Edit License</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h3 class="mb-1 fw-bold text-light-text">
                <i class="fas fa-key me-2 text-warning"></i>Update License Status
            </h3>
            <p class="text-secondary mb-0">Change the license status for <?= esc($org['name']) ?></p>
        </div>
        <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
            <i class="fas fa-arrow-left me-2"></i> Back to Organization
        </a>
    </div>

    <!-- License Edit Form -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark py-3">
                    <h5 class="fw-bold mb-0">
                        <i class="fas fa-key me-2"></i>License Status Update
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <div class="d-inline-block p-3 rounded-circle bg-warning bg-opacity-25 mb-3">
                            <i class="fas fa-key text-warning" style="font-size: 2rem;"></i>
                        </div>
                        <h5 class="text-light-text">Update Organization License</h5>
                        <p class="text-secondary">Change the license status for this organization</p>
                    </div>

                    <?= form_open('dakoii/organization/license/update/' . $org['orgcode']) ?>

                    <div class="mb-4">
                        <label for="license_status_select" class="form-label text-light-text fw-bold">License Status</label>
                        <select class="form-select form-select-lg" name="license_status" id="license_status_select" required>
                            <option value="">Select License Status</option>
                            <option value="paid" <?= $org['license_status'] == 'paid' ? 'selected' : '' ?>>Paid</option>
                            <option value="unpaid" <?= $org['license_status'] == 'unpaid' ? 'selected' : '' ?>>Unpaid</option>
                            <option value="trial" <?= $org['license_status'] == 'trial' ? 'selected' : '' ?>>Trial</option>
                        </select>
                        <div class="form-text text-secondary mt-2">
                            <ul class="ps-3 mb-0">
                                <li><b>Paid</b>: Full access to all features</li>
                                <li><b>Unpaid</b>: Limited access, payment required</li>
                                <li><b>Trial</b>: Temporary access for evaluation</li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <input type="hidden" name="id" value="<?= $org['id'] ?>">
                        <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">

                        <button type="submit" class="btn btn-warning text-dark fw-bold btn-lg">
                            <i class="fas fa-save me-2"></i> Update License Status
                        </button>

                        <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
                            <i class="fas fa-times me-2"></i> Cancel
                        </a>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
