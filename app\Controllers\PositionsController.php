<?php

namespace App\Controllers;

class PositionsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = \Config\Services::session();
        // Remove all model initialization for UI development
    }

    public function positions_groups($exercise_id)
    {
        // Using dummy data for UI development - replace with actual model calls later
        $exercises = [
            1 => [
                'id' => 1,
                'exercise_name' => 'IT Department Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-15',
                'advertisement_no' => 'ADV-2024-001',
                'advertisement_date' => '2024-01-20',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-25',
                'publish_date_to' => '2024-12-31',
                'description' => 'Recruitment exercise for various IT positions including software engineers, system administrators, and database specialists.',
                'status' => 'draft'
            ],
            2 => [
                'id' => 2,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-16',
                'advertisement_no' => 'ADV-2024-002',
                'advertisement_date' => '2024-01-21',
                'mode_of_advertisement' => 'Print Media',
                'publish_date_from' => '2024-01-26',
                'publish_date_to' => '2024-11-30',
                'description' => 'Recruitment exercise for finance positions including accountants, financial analysts, and budget officers.',
                'status' => 'draft'
            ],
            3 => [
                'id' => 3,
                'exercise_name' => 'Human Resources Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-003',
                'gazzetted_date' => '2024-01-17',
                'advertisement_no' => 'ADV-2024-003',
                'advertisement_date' => '2024-01-22',
                'mode_of_advertisement' => 'Online',
                'publish_date_from' => '2024-01-27',
                'publish_date_to' => '2024-10-31',
                'description' => 'Recruitment exercise for HR positions including HR officers, recruitment specialists, and training coordinators.',
                'status' => 'draft'
            ],
            4 => [
                'id' => 4,
                'exercise_name' => 'Engineering Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-004',
                'gazzetted_date' => '2024-01-18',
                'advertisement_no' => 'ADV-2024-004',
                'advertisement_date' => '2024-01-23',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-28',
                'publish_date_to' => '2024-09-30',
                'description' => 'Recruitment exercise for engineering positions including civil engineers, mechanical engineers, and project managers.',
                'status' => 'draft'
            ]
        ];

        $exercise = $exercises[$exercise_id] ?? null;
        if (!$exercise) {
            return redirect()->to('positions/positions_exercises')
                            ->with('error', 'Exercise not found');
        }

        // Dummy position groups data based on PositionsGroupModel structure
        $groups = [
            [
                'id' => 1,
                'org_id' => 1,
                'exercise_id' => $exercise_id,
                'parent_id' => null,
                'group_name' => 'Senior Level Positions',
                'description' => 'Senior management and leadership positions',
                'parent_name' => null,
                'position_count' => 3
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'exercise_id' => $exercise_id,
                'parent_id' => null,
                'group_name' => 'Mid Level Positions',
                'description' => 'Mid-level professional positions',
                'parent_name' => null,
                'position_count' => 4
            ],
            [
                'id' => 3,
                'org_id' => 1,
                'exercise_id' => $exercise_id,
                'parent_id' => 1,
                'group_name' => 'Technical Leadership',
                'description' => 'Technical leadership sub-group',
                'parent_name' => 'Senior Level Positions',
                'position_count' => 2
            ]
        ];

        $data = [
            'title' => 'Position Groups',
            'menu' => 'positions',
            'exercise' => $exercise,
            'positions_groups' => $groups
        ];

        return view('positions/positions_group', $data);
    }

    public function addPositionGroup()
    {
        $exercise_id = $this->request->getPost('exercise_id');
        if (!$exercise_id) {
            session()->setFlashdata('error', 'Exercise ID is required');
            return redirect()->to('positions/positions_exercises');
        }

        // Simulate successful addition for UI development
        session()->setFlashdata('success', 'Position group added successfully');
        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function updatePositionGroup()
    {
        $exercise_id = $this->request->getPost('exercise_id');

        // Simulate successful update for UI development
        session()->setFlashdata('success', 'Position group updated successfully');
        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function deletePositionGroup($id)
    {
        // For UI development, assume exercise_id = 1
        $exercise_id = 1;

        // Simulate successful deletion for UI development
        session()->setFlashdata('success', 'Position group deleted successfully');
        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function positions_exercises()
    {
        // Using dummy data for UI development - replace with actual model calls later
        $exercises = [
            [
                'id' => 1,
                'org_id' => 1,
                'exercise_name' => 'IT Department Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-15',
                'advertisement_no' => 'ADV-2024-001',
                'advertisement_date' => '2024-01-20',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-25',
                'publish_date_to' => '2024-12-31',
                'description' => 'Recruitment exercise for various IT positions including software engineers, system administrators, and database specialists.',
                'status' => 'draft',
                'group_count' => 3,
                'position_count' => 8,
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-16',
                'advertisement_no' => 'ADV-2024-002',
                'advertisement_date' => '2024-01-21',
                'mode_of_advertisement' => 'Print Media',
                'publish_date_from' => '2024-01-26',
                'publish_date_to' => '2024-11-30',
                'description' => 'Recruitment exercise for finance positions including accountants, financial analysts, and budget officers.',
                'status' => 'draft',
                'group_count' => 2,
                'position_count' => 5,
                'created_at' => '2024-01-09 11:15:00'
            ],
            [
                'id' => 3,
                'org_id' => 1,
                'exercise_name' => 'Human Resources Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-003',
                'gazzetted_date' => '2024-01-17',
                'advertisement_no' => 'ADV-2024-003',
                'advertisement_date' => '2024-01-22',
                'mode_of_advertisement' => 'Online',
                'publish_date_from' => '2024-01-27',
                'publish_date_to' => '2024-10-31',
                'description' => 'Recruitment exercise for HR positions including HR officers, recruitment specialists, and training coordinators.',
                'status' => 'draft',
                'group_count' => 2,
                'position_count' => 4,
                'created_at' => '2024-01-08 14:30:00'
            ],
            [
                'id' => 4,
                'org_id' => 1,
                'exercise_name' => 'Engineering Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-004',
                'gazzetted_date' => '2024-01-18',
                'advertisement_no' => 'ADV-2024-004',
                'advertisement_date' => '2024-01-23',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-28',
                'publish_date_to' => '2024-09-30',
                'description' => 'Recruitment exercise for engineering positions including civil engineers, mechanical engineers, and project managers.',
                'status' => 'draft',
                'group_count' => 4,
                'position_count' => 12,
                'created_at' => '2024-01-07 16:45:00'
            ]
        ];

        return view('positions/positions_exercises', [
            'title' => 'Position Exercises',
            'menu' => 'positions',
            'exercises' => $exercises
        ]);
    }

    public function exercises_list()
    {
        // Same dummy data as above for AJAX response
        $exercises = [
            [
                'id' => 1,
                'org_id' => 1,
                'exercise_name' => 'IT Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-15',
                'advertisement_no' => 'ADV-2024-001',
                'advertisement_date' => '2024-01-20',
                'mode_of_advertisement' => 'Online',
                'publish_date_from' => '2024-01-25',
                'publish_date_to' => '2024-12-31',
                'description' => 'Recruitment exercise for IT positions',
                'status' => 'draft',
                'group_count' => 3,
                'position_count' => 8,
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'exercise_name' => 'Finance Department Recruitment',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-16',
                'advertisement_no' => 'ADV-2024-002',
                'advertisement_date' => '2024-01-21',
                'mode_of_advertisement' => 'Print & Online',
                'publish_date_from' => '2024-01-26',
                'publish_date_to' => '2024-11-30',
                'description' => 'Recruitment exercise for finance positions',
                'status' => 'draft',
                'group_count' => 2,
                'position_count' => 5,
                'created_at' => '2024-01-09 11:15:00'
            ],
            [
                'id' => 3,
                'org_id' => 1,
                'exercise_name' => 'Human Resources Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-003',
                'gazzetted_date' => '2024-01-17',
                'advertisement_no' => 'ADV-2024-003',
                'advertisement_date' => '2024-01-22',
                'mode_of_advertisement' => 'Online',
                'publish_date_from' => '2024-01-27',
                'publish_date_to' => '2024-10-31',
                'description' => 'Recruitment exercise for HR positions',
                'status' => 'draft',
                'group_count' => 2,
                'position_count' => 4,
                'created_at' => '2024-01-08 14:30:00'
            ],
            [
                'id' => 4,
                'org_id' => 1,
                'exercise_name' => 'Engineering Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-004',
                'gazzetted_date' => '2024-01-18',
                'advertisement_no' => 'ADV-2024-004',
                'advertisement_date' => '2024-01-23',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-28',
                'publish_date_to' => '2024-09-30',
                'description' => 'Recruitment exercise for engineering positions',
                'status' => 'draft',
                'group_count' => 4,
                'position_count' => 12,
                'created_at' => '2024-01-07 16:45:00'
            ]
        ];

        return $this->response->setJSON(['data' => $exercises]);
    }

    public function view_positions($group_id)
    {
        // Dummy group data based on PositionsGroupModel structure
        $group = [
            'id' => $group_id,
            'org_id' => 1,
            'exercise_id' => 1,
            'parent_id' => null,
            'group_name' => 'Senior Level Positions',
            'description' => 'Senior management and leadership positions'
        ];

        // Dummy positions data based on PositionsModel structure
        $positions = [
            [
                'id' => 1,
                'exercise_id' => 1,
                'org_id' => 1,
                'position_group_id' => $group_id,
                'position_reference' => 'POS-2024-001',
                'designation' => 'Senior Software Engineer',
                'classification' => 'Professional',
                'award' => 'Grade 12',
                'location' => 'Port Moresby',
                'annual_salary' => 'K80,000 - K100,000',
                'qualifications' => 'Bachelor degree in Computer Science or related field',
                'knowledge' => 'Advanced programming skills, system architecture',
                'skills_competencies' => 'Leadership, problem-solving, technical expertise',
                'job_experiences' => '5+ years in software development',
                'jd_filepath' => 'public/uploads/jd_files/senior_software_engineer.pdf',
                'status' => 'active',
                'remarks' => 'Critical position for IT department'
            ],
            [
                'id' => 2,
                'exercise_id' => 1,
                'org_id' => 1,
                'position_group_id' => $group_id,
                'position_reference' => 'POS-2024-002',
                'designation' => 'IT Manager',
                'classification' => 'Management',
                'award' => 'Grade 14',
                'location' => 'Port Moresby',
                'annual_salary' => 'K120,000 - K150,000',
                'qualifications' => 'Masters degree in IT Management or related field',
                'knowledge' => 'IT strategy, team management, budget planning',
                'skills_competencies' => 'Strategic thinking, leadership, communication',
                'job_experiences' => '8+ years in IT with 3+ years management experience',
                'jd_filepath' => 'public/uploads/jd_files/it_manager.pdf',
                'status' => 'active',
                'remarks' => 'Department head position'
            ]
        ];

        $data = [
            'title' => 'View Positions',
            'menu' => 'positions',
            'group_id' => $group_id,
            'group' => $group,
            'positions' => $positions
        ];

        return view('positions/positions_view', $data);
    }

    public function addPosition()
    {
        // Simulate successful addition for UI development
        return $this->response->setJSON(['success' => true, 'message' => 'Position added successfully']);
    }

    public function updatePosition()
    {
        // Simulate successful update for UI development
        return $this->response->setJSON(['success' => true, 'message' => 'Position updated successfully']);
    }

    public function deletePosition($id)
    {
        // Simulate successful deletion for UI development
        return $this->response->setJSON(['success' => true, 'message' => 'Position deleted successfully']);
    }

    public function downloadJD($id)
    {
        // Simulate file download for UI development
        return $this->response->setJSON(['success' => false, 'message' => 'File download not available in UI development mode']);
    }
}
