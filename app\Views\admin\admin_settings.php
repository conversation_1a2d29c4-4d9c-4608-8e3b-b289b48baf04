<?php
/**
 * View file for main settings dashboard
 *
 * @var array $organization Organization details
 * @var array $system_settings System settings
 * @var array $user_preferences User preferences
 * @var array $security_settings Security settings
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Settings</h2>
                    <p class="text-muted mb-0">Manage system settings, organization details, and preferences</p>
                </div>
                <div>
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Categories -->
    <div class="row">
        <!-- Organization Settings -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        Organization Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <?php if (!empty($organization['orglogo'])): ?>
                            <img src="<?= base_url($organization['orglogo']) ?>" 
                                 alt="Organization Logo" 
                                 class="rounded me-3" 
                                 style="width: 60px; height: 60px; object-fit: cover;">
                        <?php else: ?>
                            <div class="bg-light rounded d-flex align-items-center justify-content-center me-3" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-building text-muted"></i>
                            </div>
                        <?php endif; ?>
                        <div>
                            <h6 class="mb-1"><?= esc($organization['name']) ?></h6>
                            <small class="text-muted"><?= esc($organization['orgcode']) ?></small>
                        </div>
                    </div>
                    <p class="text-muted mb-3"><?= esc($organization['description']) ?></p>
                    <div class="mb-3">
                        <span class="badge bg-<?= $organization['license_status'] === 'active' ? 'success' : 'warning' ?>">
                            License: <?= ucfirst($organization['license_status']) ?>
                        </span>
                        <span class="badge bg-<?= $organization['is_active'] ? 'success' : 'danger' ?> ms-2">
                            Status: <?= $organization['is_active'] ? 'Active' : 'Inactive' ?>
                        </span>
                    </div>
                    <div class="d-grid">
                        <a href="<?= base_url('settings/organization') ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            Manage Organization
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        System Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <small class="text-muted d-block">Notifications</small>
                                <div>
                                    <i class="fas fa-envelope text-<?= $system_settings['email_notifications'] ? 'success' : 'muted' ?>"></i>
                                    <span class="ms-1">Email</span>
                                </div>
                                <div>
                                    <i class="fas fa-sms text-<?= $system_settings['sms_notifications'] ? 'success' : 'muted' ?>"></i>
                                    <span class="ms-1">SMS</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <small class="text-muted d-block">Backup</small>
                                <div>
                                    <i class="fas fa-database text-<?= $system_settings['auto_backup'] ? 'success' : 'muted' ?>"></i>
                                    <span class="ms-1"><?= ucfirst($system_settings['backup_frequency']) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block">System Limits</small>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-upload me-1"></i> Max Upload: <?= $system_settings['max_file_upload_size'] ?></li>
                            <li><i class="fas fa-clock me-1"></i> Session: <?= $system_settings['session_timeout'] ?></li>
                            <li><i class="fas fa-key me-1"></i> Password Expiry: <?= $system_settings['password_expiry_days'] ?> days</li>
                        </ul>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-success" onclick="showComingSoon('System Settings')">
                            <i class="fas fa-cog me-1"></i>
                            Configure System
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Preferences -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-cog me-2"></i>
                        User Preferences
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <small class="text-muted d-block">Display</small>
                                <div class="small">
                                    <div><strong>Theme:</strong> <?= ucfirst($user_preferences['theme']) ?></div>
                                    <div><strong>Language:</strong> <?= ucfirst($user_preferences['language']) ?></div>
                                    <div><strong>Items/Page:</strong> <?= $user_preferences['items_per_page'] ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <small class="text-muted d-block">Formats</small>
                                <div class="small">
                                    <div><strong>Date:</strong> <?= $user_preferences['date_format'] ?></div>
                                    <div><strong>Time:</strong> <?= $user_preferences['time_format'] ?></div>
                                    <div><strong>Timezone:</strong> Port Moresby</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block">Dashboard Widgets</small>
                        <div class="small">
                            <?php foreach ($user_preferences['dashboard_widgets'] as $widget => $enabled): ?>
                                <div>
                                    <i class="fas fa-<?= $enabled ? 'check text-success' : 'times text-muted' ?> me-1"></i>
                                    <?= ucwords(str_replace('_', ' ', $widget)) ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-info" onclick="showComingSoon('User Preferences')">
                            <i class="fas fa-user-edit me-1"></i>
                            Edit Preferences
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Security Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted d-block">Authentication</small>
                        <div class="small">
                            <div>
                                <i class="fas fa-mobile-alt text-<?= $security_settings['two_factor_auth'] ? 'success' : 'muted' ?> me-1"></i>
                                Two-Factor Auth: <?= $security_settings['two_factor_auth'] ? 'Enabled' : 'Disabled' ?>
                            </div>
                            <div>
                                <i class="fas fa-lock me-1"></i>
                                Password Complexity: <?= ucfirst($security_settings['password_complexity']) ?>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block">Audit & Logging</small>
                        <div class="small">
                            <div><strong>Login History:</strong> <?= $security_settings['login_history_retention'] ?></div>
                            <div><strong>Audit Logs:</strong> <?= $security_settings['audit_log_retention'] ?></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block">Access Control</small>
                        <div class="small">
                            <div>
                                <i class="fas fa-globe text-<?= $security_settings['ip_whitelist_enabled'] ? 'success' : 'muted' ?> me-1"></i>
                                IP Whitelist: <?= $security_settings['ip_whitelist_enabled'] ? 'Enabled' : 'Disabled' ?>
                            </div>
                            <div>
                                <i class="fas fa-shield-alt me-1"></i>
                                Session Security: <?= ucfirst($security_settings['session_security']) ?>
                            </div>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-warning" onclick="showComingSoon('Security Settings')">
                            <i class="fas fa-shield-alt me-1"></i>
                            Security Options
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="showComingSoon('System Backup')">
                                <i class="fas fa-download me-1"></i>
                                Backup System
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success btn-sm w-100" onclick="showComingSoon('Clear Cache')">
                                <i class="fas fa-broom me-1"></i>
                                Clear Cache
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info btn-sm w-100" onclick="showComingSoon('System Logs')">
                                <i class="fas fa-file-alt me-1"></i>
                                View Logs
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning btn-sm w-100" onclick="showComingSoon('System Health')">
                                <i class="fas fa-heartbeat me-1"></i>
                                System Health
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showComingSoon(feature) {
    alert('Coming Soon!\n\n' + feature + ' functionality will be available in a future update.');
}
</script>
<?= $this->endSection() ?>
