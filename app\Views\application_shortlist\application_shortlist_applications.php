<?php
/**
 * View file for listing applications for shortlisting
 *
 * @var array $exercise Exercise details
 * @var array $position Position details
 * @var array $applications List of applications for the position
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting') ?>">Shortlisting</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting/positions/' . $exercise['id']) ?>">
                            <?= esc($exercise['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-user-check me-2"></i>Ranked Applicants</h2>
            <div class="card bg-light">
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-md-4">
                            <strong><?= esc($exercise['exercise_name']) ?></strong><br>
                            <strong><?= esc($position['position_group_name']) ?></strong>
                        </div>
                        <div class="col-md-4">
                            <strong><?= esc($position['reference']) ?> - <?= esc($position['designation']) ?></strong>
                        </div>
                        <div class="col-md-4">
                            <strong>Classification:</strong> <?= esc($position['classification']) ?><br>
                            <strong>Location:</strong> <?= esc($position['location']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('shortlisting/positions/' . $exercise['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Positions
            </a>
        </div>
    </div>

    <!-- Filters and Bulk Actions -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="bulkAction">
                                <option value="">Bulk Actions</option>
                                <option value="shortlist">Shortlist Selected</option>
                                <option value="eliminate">Eliminate Selected</option>
                                <option value="withdraw">Withdraw Selected</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-primary btn-sm" onclick="applyBulkAction()">Apply</button>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select form-select-sm" id="sortBy">
                                <option value="rating">Sort by - Rating</option>
                                <option value="name">Sort by - Name</option>
                                <option value="application_date">Sort by - Application Date</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select form-select-sm" id="statusFilter">
                                <option value="">Status - All</option>
                                <option value="pending">Pending</option>
                                <option value="shortlisted">Shortlisted</option>
                                <option value="eliminated">Eliminated</option>
                                <option value="withdrawn">Withdrawn</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No applications found for this position.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="applicationsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="3%">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th width="5%">Rank</th>
                                <th width="20%">Name</th>
                                <th width="12%">Position No.</th>
                                <th width="15%">Position</th>
                                <th width="12%">Total Rating</th>
                                <th width="10%">Status</th>
                                <th width="15%">Reason</th>
                                <th width="18%">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $rank = 1; ?>
                            <?php foreach ($applications as $application): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input application-checkbox"
                                               value="<?= $application['id'] ?>">
                                    </td>
                                    <td><?= $rank++ ?></td>
                                    <td>
                                        <strong><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></strong>
                                    </td>
                                    <td><?= esc($position['reference']) ?></td>
                                    <td><?= esc($position['designation']) ?></td>
                                    <td><?= $application['rating_total'] ?> / <?= $application['rating_max'] ?></td>
                                    <td>
                                        <?php
                                        $statusClass = 'secondary';
                                        $statusText = 'Active';

                                        switch($application['shortlist_status']) {
                                            case 'shortlisted':
                                                $statusClass = 'success';
                                                $statusText = 'Shortlisted';
                                                break;
                                            case 'eliminated':
                                                $statusClass = 'danger';
                                                $statusText = 'Eliminated';
                                                break;
                                            case 'withdrawn':
                                                $statusClass = 'warning';
                                                $statusText = 'Withdrawn';
                                                break;
                                            default:
                                                $statusClass = 'info';
                                                $statusText = 'Active';
                                                break;
                                        }
                                        ?>
                                        <?= $statusText ?>
                                    </td>
                                    <td><?= esc($application['remarks'] ?? '') ?></td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm me-1"
                                                onclick="openActionModal(<?= $application['id'] ?>, 'shortlist')">
                                            Shortlist
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm me-1"
                                                onclick="openActionModal(<?= $application['id'] ?>, 'eliminate')">
                                            Eliminate
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm"
                                                onclick="openActionModal(<?= $application['id'] ?>, 'withdraw')">
                                            Withdraw
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalLabel">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('', ['id' => 'actionForm']) ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="actionType" class="form-label"><strong>Action</strong></label>
                    <input type="text" class="form-control" id="actionType" name="shortlist_status" readonly>
                </div>
                <div class="mb-3">
                    <label for="remarks" class="form-label"><strong>Remarks/Reason</strong></label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3"
                              placeholder="Enter reason for this action..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary" id="confirmAction">Confirm</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        responsive: true,
        order: [[5, 'desc']], // Sort by rating descending
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications found for this position",
        },
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable sorting for checkbox and action columns
        ]
    });

    // Select all checkbox functionality
    $('#selectAll').change(function() {
        $('.application-checkbox').prop('checked', this.checked);
    });

    // Individual checkbox change
    $('.application-checkbox').change(function() {
        if (!this.checked) {
            $('#selectAll').prop('checked', false);
        } else if ($('.application-checkbox:checked').length === $('.application-checkbox').length) {
            $('#selectAll').prop('checked', true);
        }
    });

    // Filter functionality
    $('#statusFilter').change(function() {
        var status = $(this).val();
        var table = $('#applicationsTable').DataTable();

        if (status === '') {
            table.column(6).search('').draw(); // Status column
        } else {
            table.column(6).search(status).draw();
        }
    });

    // Sort functionality
    $('#sortBy').change(function() {
        var sortBy = $(this).val();
        var table = $('#applicationsTable').DataTable();

        switch(sortBy) {
            case 'rating':
                table.order([5, 'desc']).draw();
                break;
            case 'name':
                table.order([2, 'asc']).draw();
                break;
            case 'application_date':
                table.order([1, 'asc']).draw();
                break;
        }
    });
});

// Open action modal
function openActionModal(applicationId, action) {
    $('#actionType').val(action);
    $('#actionModalLabel').text('Confirm ' + action.charAt(0).toUpperCase() + action.slice(1));

    // Set form action URL
    $('#actionForm').attr('action', '<?= base_url('shortlisting/update/') ?>' + applicationId);

    // Clear previous remarks
    $('#remarks').val('');

    // Show modal
    $('#actionModal').modal('show');
}

// Bulk action functionality
function applyBulkAction() {
    var selectedIds = [];
    $('.application-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
    });

    if (selectedIds.length === 0) {
        toastr.warning('Please select at least one application.');
        return;
    }

    var action = $('#bulkAction').val();
    if (!action) {
        toastr.warning('Please select a bulk action.');
        return;
    }

    // For demo purposes, show confirmation
    if (confirm('Are you sure you want to ' + action + ' ' + selectedIds.length + ' selected application(s)?')) {
        toastr.success('Bulk action "' + action + '" applied to ' + selectedIds.length + ' application(s) (mock mode).');

        // Reset selections
        $('.application-checkbox').prop('checked', false);
        $('#selectAll').prop('checked', false);
        $('#bulkAction').val('');
    }
}
</script>
<?= $this->endSection() ?>
