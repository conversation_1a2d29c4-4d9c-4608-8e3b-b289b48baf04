<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('rating') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('rating/positions/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-users me-2"></i>Applications for Rating</h2>
            <p class="text-muted">
                Position: <strong><?= esc($position['designation']) ?></strong><br>
                <small>Exercise: <?= esc($exercise['exercise_name']) ?> | Requirements: <?= esc($position['requirements']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('rating/positions/' . $exercise['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Positions
            </a>
        </div>
    </div>

    <!-- Applications List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No applications found for this position.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="applicationsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Application #</th>
                                <th>Applicant Name</th>
                                <th>Date Applied</th>
                                <th>Rating Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($applications as $application): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td><?= esc($application['application_number']) ?></td>
                                    <td><?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?></td>
                                    <td><?= date('d M Y', strtotime($application['created_at'])) ?></td>
                                    <td>
                                        <?php if ($application['rating_status'] === 'completed'): ?>
                                            <span class="badge bg-success">Rated</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($application['rating_status'] === 'completed'): ?>
                                            <a href="<?= base_url('rating/view/' . $application['id']) ?>"
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-eye"></i> View Rating
                                            </a>
                                        <?php else: ?>
                                            <a href="<?= base_url('rating/rate/' . $application['id']) ?>"
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-star"></i> Rate Application
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        responsive: true,
        order: [[3, 'desc']], // Sort by date applied desc
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications available for this position",
        }
    });

    console.log('Position Applications view loaded');
});
</script>
<?= $this->endSection() ?>