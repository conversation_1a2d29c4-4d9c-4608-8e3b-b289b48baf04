<?php
/**
 * View file for Application Register Report
 *
 * @var array $position Position details
 * @var array $applications List of applications
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Application Register - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Application Register Report</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?> - <?= esc($position['position_reference']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>
                        Export Excel
                    </button>
                    <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Group:</dt>
                                <dd class="col-sm-8"><?= esc($position['group_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Register -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Application Register (<?= count($applications) ?> Applications)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($applications)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No applications found</h5>
                            <p class="text-muted">No applications have been received for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="applicationsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Application No.</th>
                                        <th>Full Name</th>
                                        <th>Gender</th>
                                        <th>Date of Birth</th>
                                        <th>Place of Origin</th>
                                        <th>Contact Details</th>
                                        <th>Current Employment</th>
                                        <th>Status</th>
                                        <th>Received Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $index => $application): ?>
                                        <?php 
                                        $contact = json_decode($application['contact_details'], true);
                                        ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong><?= esc($application['application_number']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></strong>
                                                </div>
                                            </td>
                                            <td><?= esc($application['gender']) ?></td>
                                            <td><?= date('M d, Y', strtotime($application['date_of_birth'])) ?></td>
                                            <td><?= esc($application['place_of_origin']) ?></td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Phone:</strong> <?= esc($contact['phone'] ?? 'N/A') ?></div>
                                                    <div><strong>Email:</strong> <?= esc($contact['email'] ?? 'N/A') ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Employer:</strong> <?= esc($application['current_employer']) ?></div>
                                                    <div><strong>Position:</strong> <?= esc($application['current_position']) ?></div>
                                                    <div><strong>Salary:</strong> <?= esc($application['current_salary']) ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?= ucfirst($application['application_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <?= date('M d, Y', strtotime($application['received_at'])) ?>
                                                    <br>
                                                    <?= date('H:i', strtotime($application['received_at'])) ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Summary Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($applications) ?></h4>
                                <p class="mb-0">Total Applications</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">
                                    <?= count(array_filter($applications, function($app) { return $app['gender'] === 'Male'; })) ?>
                                </h4>
                                <p class="mb-0">Male Applicants</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">
                                    <?= count(array_filter($applications, function($app) { return $app['gender'] === 'Female'; })) ?>
                                </h4>
                                <p class="mb-0">Female Applicants</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">
                                    <?= count(array_filter($applications, function($app) { return $app['application_status'] === 'received'; })) ?>
                                </h4>
                                <p class="mb-0">Received Applications</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // Mock export functionality for UI development
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show success message
        alert('Application Register report exported successfully!\n\nFile: application_register_' + new Date().toISOString().slice(0,10) + '.xlsx');
    }, 2000);
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $('#applicationsTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[1, 'asc']],
            columnDefs: [
                { orderable: false, targets: [6, 7] }
            ]
        });
    }
});
</script>
<?= $this->endSection() ?>
