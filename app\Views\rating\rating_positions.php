<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('rating') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-briefcase me-2"></i>Positions for Rating</h2>
            <p class="text-muted">
                Exercise: <strong><?= esc($exercise['exercise_name']) ?></strong><br>
                <small>Exercise ID: <?= esc($exercise['id']) ?> | Status: <?= ucfirst($exercise['status']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('rating') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <!-- Positions List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No positions available in this exercise.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="25%">Position</th>
                                <th width="15%">Reference</th>
                                <th width="20%">Group</th>
                                <th width="10%">Applications</th>
                                <th width="25%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td>
                                        <strong><?= esc($position['designation']) ?></strong>
                                        <br><small class="text-muted"><?= esc($position['requirements']) ?></small>
                                    </td>
                                    <td><?= esc($position['position_reference']) ?></td>
                                    <td><?= esc($position['group_name']) ?></td>
                                    <td>
                                        <span class="badge bg-info"><?= $position['application_count'] ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('rating/applications/' . $position['id']) ?>"
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-users"></i> View Applications
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by row number
        language: {
            search: "Search positions:",
            lengthMenu: "Show _MENU_ positions per page",
            info: "Showing _START_ to _END_ of _TOTAL_ positions",
            emptyTable: "No positions available in this exercise",
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    console.log('Exercise Positions view loaded');
});
</script>
<?= $this->endSection() ?>