<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Provinces</h5>
                    <a href="<?= base_url('dakoii/province/create') ?>" class="btn btn-light">
                        <i class="fas fa-plus"></i> Add Province
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>#</th>
                                <th>Prov. Code</th>
                                <th>JSON ID</th>
                                <th>Name</th>
                                <th>Districts</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1;
                            foreach ($provinces as $province): ?>
                                <tr>
                                    <td><?= $i++ ?></td>
                                    <td><?= esc($province['provincecode']) ?></td>
                                    <td><?= esc($province['json_id']) ?></td>
                                    <td><?= esc($province['name']) ?></td>
                                    <td>
                                        <a href="<?= base_url('dakoii/district/list/' . $province['id']) ?>"
                                            class="btn btn-sm btn-info">
                                            <i class="fas fa-list"></i> Manage Districts
                                        </a>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('dakoii/province/edit/' . $province['id']) ?>"
                                           class="btn btn-sm btn-primary"
                                           title="Edit Province">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('dakoii/province/delete/' . $province['id']) ?>"
                                            class="btn btn-sm btn-danger"
                                            title="Delete Province"
                                            onclick="return confirm('Are you sure you want to delete the province <?= esc($province['name']) ?>? This action cannot be undone.')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Province JSON Data</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>FID</th>
                                            <th>Province ID</th>
                                            <th>Province Name</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $json_file = file_get_contents(base_url(JSON_MAP_PROVINCE));
                                        $data = json_decode($json_file, true);

                                        foreach ($data['features'] as $feature) {
                                            $properties = $feature['properties'];
                                            ?>
                                            <tr>
                                                <td><?= esc($properties['FID']) ?></td>
                                                <td><?= esc($properties['PROVID']) ?></td>
                                                <td><?= esc($properties['PROVNAME']) ?></td>
                                            </tr>
                                            <?php
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>