<?= $this->extend('templates/dakoiitemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Create New Organization</h3>
                    <div class="card-tools">
                        <a href="<?= base_url('dakoii/organization/list') ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?= form_open_multipart('dakoii/organization/create', ['class' => 'form-horizontal']) ?>

                    <div class="form-group row">
                        <label for="org_name" class="col-sm-2 col-form-label">Organization Name <span class="text-danger">*</span></label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="org_name" name="org_name" required>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="description" class="col-sm-2 col-form-label">Description</label>
                        <div class="col-sm-10">
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="postal_address" class="col-sm-2 col-form-label">Postal Address</label>
                        <div class="col-sm-10">
                            <textarea class="form-control" id="postal_address" name="postal_address" rows="2"></textarea>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="phone_numbers" class="col-sm-2 col-form-label">Phone Numbers</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="phone_numbers" name="phone_numbers" placeholder="e.g., +************, +************">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="email_addresses" class="col-sm-2 col-form-label">Email Addresses</label>
                        <div class="col-sm-10">
                            <input type="email" class="form-control" id="email_addresses" name="email_addresses" placeholder="e.g., <EMAIL>">
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="location_lock_country" class="col-sm-2 col-form-label">Location Lock Country</label>
                        <div class="col-sm-10">
                            <select class="form-control" id="location_lock_country" name="location_lock_country">
                                <option value="">Select Country (Optional)</option>
                                <?php if (isset($countries) && !empty($countries)): ?>
                                    <?php foreach ($countries as $country): ?>
                                        <option value="<?= $country['id'] ?>"><?= esc($country['name']) ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="location_lock_province" class="col-sm-2 col-form-label">Location Lock Province</label>
                        <div class="col-sm-10">
                            <select class="form-control" id="location_lock_province" name="location_lock_province">
                                <option value="">Select Province (Optional)</option>
                                <?php if (isset($provinces) && !empty($provinces)): ?>
                                    <?php foreach ($provinces as $province): ?>
                                        <option value="<?= $province['id'] ?>"><?= esc($province['name']) ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-10 offset-sm-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_locationlocked" name="is_locationlocked" value="1">
                                <label class="form-check-label" for="is_locationlocked">
                                    Enable Location Lock
                                </label>
                                <small class="form-text text-muted">Restrict organization to selected location</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="org_logo" class="col-sm-2 col-form-label">Organization Logo</label>
                        <div class="col-sm-10">
                            <input type="file" class="form-control-file" id="org_logo" name="org_logo" accept="image/*">
                            <small class="form-text text-muted">Upload organization logo (optional)</small>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-10 offset-sm-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Organization
                            </button>
                            <a href="<?= base_url('dakoii/organization/list') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
