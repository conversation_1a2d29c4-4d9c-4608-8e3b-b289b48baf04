<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item active" aria-current="page">Exercises</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-star me-2"></i>Application Rating</h2>
            <p class="text-muted">Select an exercise to view positions and rate applications</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url() ?>dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Exercises List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($exercises)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No exercises available for rating.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="exercisesTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="30%">Exercise Name</th>
                                <th width="15%">Exercise Code</th>
                                <th width="15%">Department</th>
                                <th width="15%">Status</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($exercises as $exercise): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td><?= esc($exercise['exercise_name']) ?></td>
                                    <td><?= esc($exercise['exercise_code']) ?></td>
                                    <td><?= esc($exercise['department']) ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?= ucfirst($exercise['status']) ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('rating/positions/' . $exercise['id']) ?>"
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-briefcase"></i> View Positions
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#exercisesTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by ID asc
        language: {
            search: "Search exercises:",
            lengthMenu: "Show _MENU_ exercises per page",
            info: "Showing _START_ to _END_ of _TOTAL_ exercises",
            emptyTable: "No exercises available for rating",
        }
    });
});
</script>
<?= $this->endSection() ?>