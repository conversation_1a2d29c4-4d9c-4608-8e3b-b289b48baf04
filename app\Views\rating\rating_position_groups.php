<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Rating - Position Groups</h2>
                    <p class="text-muted mb-0">Select a position group to continue</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('rating') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?= esc($exercise['exercise_name']) ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Exercise Info Card -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Exercise Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2"><strong>Exercise Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                    <p class="mb-2"><strong>Exercise ID:</strong> <?= esc($exercise['id']) ?></p>
                </div>
                <div class="col-md-6">
                    <p class="mb-2"><strong>Created Date:</strong> <?= date('d M Y', strtotime($exercise['created_at'])) ?></p>
                    <p class="mb-2"><strong>Status:</strong>
                        <span class="badge bg-primary bg-opacity-10 text-primary"><?= ucfirst($exercise['status']) ?></span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Groups List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Position Groups</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Group Name</th>
                            <th>Group ID</th>
                            <th>Created Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($positionGroups)): ?>
                            <tr>
                                <td colspan="4" class="text-center py-4">No position groups found for rating</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($positionGroups as $group): ?>
                                <tr>
                                    <td class="px-4 fw-medium"><?= esc($group['group_name']) ?></td>
                                    <td><?= esc($group['id']) ?></td>
                                    <td><?= date('d M Y', strtotime($group['created_at'])) ?></td>
                                    <td>
                                        <a href="<?= base_url('rating/positions/' . $group['id']) ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-arrow-right me-1"></i> Select
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Position groups page loaded');
});
</script>
<?= $this->endSection() ?>