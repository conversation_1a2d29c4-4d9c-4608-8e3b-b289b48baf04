<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('rating') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('rating/positions/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('rating/applications/' . $position['id']) ?>">Applications</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Rate Application
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-star me-2"></i>Rate Application</h2>
            <p class="text-muted">
                Applicant: <strong><?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?></strong><br>
                <small>Position: <?= esc($position['designation']) ?> | Application #: <?= esc($application['application_number']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('rating/applications/' . $position['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Applications
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Applicant Information Panel -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Position Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Position:</strong><br>
                        <span class="text-muted"><?= esc($position['designation']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Position No:</strong><br>
                        <span class="text-muted"><?= esc($position['id']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Qualifications:</strong><br>
                        <span class="text-muted"><?= esc($position['requirements']) ?></span>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Applicant Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Name:</strong><br>
                        <span class="text-muted"><?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Application #:</strong><br>
                        <span class="text-muted"><?= esc($application['application_number']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Gender:</strong><br>
                        <span class="text-muted"><?= esc($application['gender']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Age:</strong><br>
                        <span class="text-muted">
                            <?php
                            $birthDate = new DateTime($application['date_of_birth']);
                            $today = new DateTime();
                            $age = $today->diff($birthDate)->y;
                            echo $age . ' years';
                            ?>
                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>Place of Origin:</strong><br>
                        <span class="text-muted"><?= esc($application['place_of_origin']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Current Employer:</strong><br>
                        <span class="text-muted"><?= esc($application['current_employer']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Current Position:</strong><br>
                        <span class="text-muted"><?= esc($application['current_position']) ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Contact Details:</strong><br>
                        <span class="text-muted"><?= esc($application['contact_details']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rating Form Panel -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>Rating Criteria</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('rating/submit') ?>" method="POST" id="ratingForm">
                        <?= csrf_field() ?>
                        <input type="hidden" name="application_id" value="<?= $application['id'] ?>">
                        <input type="hidden" name="position_id" value="<?= $position['id'] ?>">

                        <!-- Rating Criteria Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Criteria</th>
                                        <th width="150">Rating</th>
                                        <th width="100">Max Score</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ratingCriteria as $criteria): ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($criteria['name']) ?></strong><br>
                                            <small class="text-muted"><?= esc($criteria['description']) ?></small>
                                        </td>
                                        <td>
                                            <select name="ratings[<?= $criteria['id'] ?>]" class="form-select rating-select"
                                                    data-max="<?= $criteria['max_score'] ?>" required>
                                                <option value="">Select Rating</option>
                                                <?php for ($i = 0; $i <= $criteria['max_score']; $i++): ?>
                                                    <option value="<?= $i ?>"><?= $i ?></option>
                                                <?php endfor; ?>
                                            </select>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info"><?= $criteria['max_score'] ?></span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-light">
                                        <th>Total Score</th>
                                        <th>
                                            <span id="totalScore" class="fw-bold text-primary">0</span>
                                        </th>
                                        <th class="text-center">
                                            <span class="badge bg-primary">100</span>
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <!-- Remarks Section -->
                        <div class="mt-4">
                            <label for="remarks" class="form-label"><strong>Remarks</strong></label>
                            <textarea name="remarks" id="remarks" class="form-control" rows="4"
                                      placeholder="Add any additional comments or observations about this applicant..."></textarea>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-4 d-flex justify-content-end gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Submit Rating
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    const ratingSelects = document.querySelectorAll('.rating-select');
    const totalScoreElement = document.getElementById('totalScore');

    function updateTotalScore() {
        let total = 0;
        ratingSelects.forEach(select => {
            const value = parseInt(select.value) || 0;
            total += value;
        });
        totalScoreElement.textContent = total;

        // Update color based on score
        totalScoreElement.className = 'fw-bold ' +
            (total >= 80 ? 'text-success' :
             total >= 60 ? 'text-warning' : 'text-danger');
    }

    ratingSelects.forEach(select => {
        select.addEventListener('change', updateTotalScore);
    });

    // Form validation
    document.getElementById('ratingForm').addEventListener('submit', function(e) {
        let allRated = true;
        ratingSelects.forEach(select => {
            if (!select.value) {
                allRated = false;
            }
        });

        if (!allRated) {
            e.preventDefault();
            alert('Please provide ratings for all criteria before submitting.');
        }
    });

    console.log('Rating form loaded');
});
</script>
<?= $this->endSection() ?>
