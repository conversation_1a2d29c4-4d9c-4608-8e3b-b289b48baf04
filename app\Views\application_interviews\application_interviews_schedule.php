<?php
/**
 * Interview Schedule
 * View and manage interview schedule for a session
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/session/' . $session['id'] . '/positions') ?>"><?= esc($session['session_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Schedule</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Schedule</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-calendar-alt me-1"></i>
                        <?= esc($session['session_name']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/session/' . $session['id'] . '/positions') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Positions
                    </a>
                    <button type="button" class="btn btn-success me-2" id="generateScheduleBtn">
                        <i class="fas fa-magic me-2"></i>Generate Schedule
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleSettingsModal">
                        <i class="fas fa-cog me-2"></i>Schedule Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-calendar-day text-primary mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0"><?= count(array_unique(array_column($schedule, 'date'))) ?></h5>
                    <small class="text-muted">Interview Days</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-users text-success mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0"><?= count($schedule) ?></h5>
                    <small class="text-muted">Total Interviews</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-check-circle text-info mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0"><?= count(array_filter($schedule, function($s) { return $s['status'] === 'scheduled'; })) ?></h5>
                    <small class="text-muted">Scheduled</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-clipboard-check text-warning mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0"><?= count(array_filter($schedule, function($s) { return $s['status'] === 'completed'; })) ?></h5>
                    <small class="text-muted">Completed</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Interview Schedule -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        Interview Schedule
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm active" data-filter="all">
                            All
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="scheduled">
                            Scheduled
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="completed">
                            Completed
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($schedule)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-calendar-alt text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Schedule Generated</h5>
                    <p class="text-muted">Generate an interview schedule to start managing interview appointments.</p>
                    <button type="button" class="btn btn-success" id="generateScheduleBtn2">
                        <i class="fas fa-magic me-2"></i>Generate Schedule
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="scheduleTable">
                        <thead class="table-light">
                            <tr>
                                <th>Date & Time</th>
                                <th>Candidate</th>
                                <th>Position</th>
                                <th>Panel Members</th>
                                <th>Status</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($schedule as $item): ?>
                                <tr data-status="<?= $item['status'] ?>">
                                    <td>
                                        <div>
                                            <div class="fw-medium">
                                                <?= date('M d, Y', strtotime($item['date'])) ?>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?= $item['time_slot'] ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= esc($item['candidate_name']) ?></div>
                                            <small class="text-muted">
                                                <i class="fas fa-envelope me-1"></i>
                                                <?= esc($item['candidate_email']) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-dark">
                                            <?= esc($item['position']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?= esc($item['panel_members']) ?></small>
                                    </td>
                                    <td>
                                        <?php if ($item['status'] === 'scheduled'): ?>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-calendar-check me-1"></i>Scheduled
                                            </span>
                                        <?php elseif ($item['status'] === 'completed'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>Completed
                                            </span>
                                        <?php elseif ($item['status'] === 'cancelled'): ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times-circle me-1"></i>Cancelled
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <?= ucfirst($item['status']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button" 
                                                    class="btn btn-outline-primary btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#editScheduleModal"
                                                    data-schedule='<?= json_encode($item) ?>'
                                                    title="Edit Schedule">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" 
                                                    class="btn btn-outline-info btn-sm send-notification-btn" 
                                                    data-candidate-email="<?= esc($item['candidate_email']) ?>"
                                                    data-candidate-name="<?= esc($item['candidate_name']) ?>"
                                                    title="Send Notification">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <?php if ($item['status'] === 'scheduled'): ?>
                                                <button type="button" 
                                                        class="btn btn-outline-success btn-sm mark-completed-btn" 
                                                        data-schedule-id="<?= $item['id'] ?>"
                                                        title="Mark as Completed">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Schedule Settings Modal -->
<div class="modal fade" id="scheduleSettingsModal" tabindex="-1" aria-labelledby="scheduleSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="scheduleSettingsModalLabel">
                    <i class="fas fa-cog me-2"></i>Schedule Settings
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Configure interview session settings before generating the schedule.
                </div>
                <div class="d-grid">
                    <a href="<?= base_url('interviews/session/' . $session['id'] . '/settings') ?>" class="btn btn-primary">
                        <i class="fas fa-cog me-2"></i>Go to Interview Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Schedule Modal -->
<div class="modal fade" id="editScheduleModal" tabindex="-1" aria-labelledby="editScheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editScheduleModalLabel">
                    <i class="fas fa-edit me-2"></i>Edit Schedule
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= form_open('interviews/session/' . $session['id'] . '/schedule/update', ['id' => 'editScheduleForm']) ?>
                    <input type="hidden" id="edit_schedule_id" name="schedule_id">
                    
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Interview Date</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_time_slot" class="form-label">Time Slot</label>
                        <input type="text" class="form-control" id="edit_time_slot" name="time_slot" placeholder="e.g., 09:00 - 10:00" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-select" id="edit_status" name="status" required>
                            <option value="scheduled">Scheduled</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="rescheduled">Rescheduled</option>
                        </select>
                    </div>
                <?= form_close() ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editScheduleForm" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Update Schedule
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter table rows
            const rows = document.querySelectorAll('#scheduleTable tbody tr');
            rows.forEach(row => {
                if (filter === 'all' || row.getAttribute('data-status') === filter) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });

    // Generate schedule
    document.querySelectorAll('#generateScheduleBtn, #generateScheduleBtn2').forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('This will generate a new interview schedule. Any existing schedule will be replaced. Continue?')) {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
                
                fetch('<?= base_url('interviews/session/' . $session['id'] . '/schedule/generate') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (typeof toastr !== 'undefined') {
                            toastr.success(data.message);
                        } else {
                            alert(data.message);
                        }
                        window.location.reload();
                    } else {
                        if (typeof toastr !== 'undefined') {
                            toastr.error(data.message);
                        } else {
                            alert('Error: ' + data.message);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (typeof toastr !== 'undefined') {
                        toastr.error('An error occurred while generating the schedule');
                    } else {
                        alert('An error occurred while generating the schedule');
                    }
                })
                .finally(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-magic me-2"></i>Generate Schedule';
                });
            }
        });
    });

    // Edit schedule modal
    const editScheduleModal = document.getElementById('editScheduleModal');
    if (editScheduleModal) {
        editScheduleModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const schedule = JSON.parse(button.getAttribute('data-schedule'));
            
            document.getElementById('edit_schedule_id').value = schedule.id;
            document.getElementById('edit_date').value = schedule.date;
            document.getElementById('edit_time_slot').value = schedule.time_slot;
            document.getElementById('edit_status').value = schedule.status;
        });
    }

    // Edit schedule form
    const editScheduleForm = document.getElementById('editScheduleForm');
    if (editScheduleForm) {
        editScheduleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    const modal = bootstrap.Modal.getInstance(editScheduleModal);
                    modal.hide();
                    window.location.reload();
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toastr !== 'undefined') {
                    toastr.error('An error occurred while updating the schedule');
                } else {
                    alert('An error occurred while updating the schedule');
                }
            });
        });
    }

    // Send notification
    document.querySelectorAll('.send-notification-btn').forEach(button => {
        button.addEventListener('click', function() {
            const email = this.getAttribute('data-candidate-email');
            const name = this.getAttribute('data-candidate-name');
            
            if (confirm(`Send interview notification to ${name} (${email})?`)) {
                // Mock notification sending
                if (typeof toastr !== 'undefined') {
                    toastr.success(`Notification sent to ${name}`);
                } else {
                    alert(`Notification sent to ${name}`);
                }
            }
        });
    });

    // Mark as completed
    document.querySelectorAll('.mark-completed-btn').forEach(button => {
        button.addEventListener('click', function() {
            const scheduleId = this.getAttribute('data-schedule-id');
            
            if (confirm('Mark this interview as completed?')) {
                // Mock completion
                if (typeof toastr !== 'undefined') {
                    toastr.success('Interview marked as completed');
                } else {
                    alert('Interview marked as completed');
                }
                window.location.reload();
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
