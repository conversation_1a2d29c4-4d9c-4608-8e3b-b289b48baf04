<?php

namespace App\Controllers;

use CodeIgniter\Controller;

/**
 * ReportsProfilingController
 * 
 * Controller for profiling reports including Form 3.7 and Form 3.7A reports
 */
class ReportsProfilingController extends Controller
{
    public function __construct()
    {
        helper(['url', 'form']);
    }

    /**
     * [GET] Form 3.7 Profiling Report
     * URI: /reports/profiling/{positionId}
     */
    public function profiling($positionId)
    {
        // Mock position data
        $position = $this->getMockPosition($positionId);
        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Mock profiling data based on application details and experiences
        $profilingData = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'date_of_birth' => '1990-05-15',
                'place_of_origin' => 'Western Province',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Single',
                'contact_details' => '{"phone": "71234567", "email": "<EMAIL>"}',
                'location_address' => 'Port Moresby, NCD',
                'current_employer' => 'ABC Company Ltd',
                'current_position' => 'Software Developer',
                'current_salary' => 'K65,000',
                'qualifications' => [
                    ['level' => 'Bachelor', 'field' => 'Computer Science', 'institution' => 'UPNG', 'year' => '2012'],
                    ['level' => 'Certificate', 'field' => 'Java Programming', 'institution' => 'Tech Institute', 'year' => '2013']
                ],
                'experiences' => [
                    ['employer' => 'ABC Company Ltd', 'position' => 'Software Developer', 'from' => '2018-01', 'to' => 'Present', 'years' => 6],
                    ['employer' => 'XYZ Tech', 'position' => 'Junior Developer', 'from' => '2015-06', 'to' => '2017-12', 'years' => 2.5]
                ],
                'total_experience_years' => 8.5,
                'pre_screening_status' => 'passed',
                'shortlist_status' => 'shortlisted'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Mary',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'date_of_birth' => '1988-08-22',
                'place_of_origin' => 'Morobe Province',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Married',
                'contact_details' => '{"phone": "72345678", "email": "<EMAIL>"}',
                'location_address' => 'Lae, Morobe',
                'current_employer' => 'XYZ Corporation',
                'current_position' => 'Systems Analyst',
                'current_salary' => 'K70,000',
                'qualifications' => [
                    ['level' => 'Master', 'field' => 'Information Systems', 'institution' => 'UNITECH', 'year' => '2014'],
                    ['level' => 'Bachelor', 'field' => 'Computer Science', 'institution' => 'UPNG', 'year' => '2010']
                ],
                'experiences' => [
                    ['employer' => 'XYZ Corporation', 'position' => 'Systems Analyst', 'from' => '2020-03', 'to' => 'Present', 'years' => 4],
                    ['employer' => 'Tech Solutions', 'position' => 'Software Engineer', 'from' => '2015-01', 'to' => '2020-02', 'years' => 5]
                ],
                'total_experience_years' => 9,
                'pre_screening_status' => 'passed',
                'shortlist_status' => 'shortlisted'
            ]
        ];

        $data = [
            'title' => 'Form 3.7 Profiling Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'profiling_data' => $profilingData
        ];

        return view('application_reports/appx_reports_profiling', $data);
    }

    /**
     * [GET] Form 3.7A Updated Profiling Report
     * URI: /reports/profiling-updated/{positionId}
     */
    public function profilingUpdated($positionId)
    {
        // Mock position data
        $position = $this->getMockPosition($positionId);
        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Mock updated profiling data with additional scoring and interview information
        $updatedProfilingData = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'date_of_birth' => '1990-05-15',
                'place_of_origin' => 'Western Province',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Single',
                'contact_details' => '{"phone": "71234567", "email": "<EMAIL>"}',
                'location_address' => 'Port Moresby, NCD',
                'current_employer' => 'ABC Company Ltd',
                'current_position' => 'Software Developer',
                'current_salary' => 'K65,000',
                'total_experience_years' => 8.5,
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 85,
                'shortlist_status' => 'shortlisted',
                'application_rating_score' => 78,
                'interview_status' => 'interviewed',
                'interview_score' => 82,
                'overall_score' => 80,
                'final_ranking' => 2,
                'selection_status' => 'selected',
                'remarks' => 'Strong technical skills and good communication. Recommended for appointment.'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Mary',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'date_of_birth' => '1988-08-22',
                'place_of_origin' => 'Morobe Province',
                'citizenship' => 'Papua New Guinea',
                'marital_status' => 'Married',
                'contact_details' => '{"phone": "72345678", "email": "<EMAIL>"}',
                'location_address' => 'Lae, Morobe',
                'current_employer' => 'XYZ Corporation',
                'current_position' => 'Systems Analyst',
                'current_salary' => 'K70,000',
                'total_experience_years' => 9,
                'pre_screening_status' => 'passed',
                'pre_screening_score' => 92,
                'shortlist_status' => 'shortlisted',
                'application_rating_score' => 88,
                'interview_status' => 'interviewed',
                'interview_score' => 90,
                'overall_score' => 89,
                'final_ranking' => 1,
                'selection_status' => 'selected',
                'remarks' => 'Exceptional candidate with excellent qualifications and experience. Top choice for appointment.'
            ]
        ];

        $data = [
            'title' => 'Form 3.7A Updated Profiling Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'updated_profiling_data' => $updatedProfilingData
        ];

        return view('application_reports/appx_reports_profiling_updated', $data);
    }

    /**
     * [POST] Export Profiling Report
     * URI: /reports/profiling/export
     */
    public function exportProfiling()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Form 3.7 profiling report exported successfully',
            'file_url' => base_url('exports/form_3_7_profiling_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * [POST] Export Updated Profiling Report
     * URI: /reports/profiling-updated/export
     */
    public function exportProfilingUpdated()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Form 3.7A updated profiling report exported successfully',
            'file_url' => base_url('exports/form_3_7a_updated_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Get mock position data
     */
    private function getMockPosition($positionId)
    {
        $positions = [
            1 => [
                'id' => 1,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Engineer',
                'classification' => 'Grade 12',
                'award' => 'K85,000 - K95,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            2 => [
                'id' => 2,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'classification' => 'Grade 11',
                'award' => 'K75,000 - K85,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            3 => [
                'id' => 3,
                'exercise_id' => 1,
                'position_group_id' => 2,
                'position_reference' => 'SYS-001',
                'designation' => 'System Administrator',
                'classification' => 'Grade 10',
                'award' => 'K65,000 - K75,000',
                'location' => 'Port Moresby',
                'group_name' => 'System Administration',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ]
        ];

        return $positions[$positionId] ?? null;
    }
}
