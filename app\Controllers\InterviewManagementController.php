<?php

namespace App\Controllers;

class InterviewManagementController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
    }

    /**
     * List exercises with selection status for interview management
     */
    public function index()
    {
        // Mock exercises data with selection status
        $exercises = $this->getMockExercises();

        $data = [
            'title' => 'Interview Management - Exercises',
            'exercises' => $exercises
        ];

        return view('application_interviews/application_interviews_index', $data);
    }

    /**
     * List interview sessions for a specific exercise
     */
    public function sessions($exerciseId)
    {
        $exercise = $this->getMockExercise($exerciseId);
        $sessions = $this->getMockInterviewSessions($exerciseId);

        $data = [
            'title' => 'Interview Sessions - ' . $exercise['exercise_name'],
            'exercise' => $exercise,
            'sessions' => $sessions
        ];

        return view('application_interviews/application_interviews_sessions', $data);
    }

    /**
     * Show create interview session form
     */
    public function createSession($exerciseId)
    {
        $exercise = $this->getMockExercise($exerciseId);

        $data = [
            'title' => 'Create Interview Session',
            'exercise' => $exercise
        ];

        return view('application_interviews/application_interviews_create', $data);
    }

    /**
     * Store new interview session
     */
    public function storeSession()
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview session created successfully',
            'session_id' => rand(1, 100)
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Show edit interview session form
     */
    public function editSession($sessionId)
    {
        $session = $this->getMockInterviewSession($sessionId);
        $exercise = $this->getMockExercise($session['exercise_id']);

        $data = [
            'title' => 'Edit Interview Session',
            'session' => $session,
            'exercise' => $exercise
        ];

        return view('application_interviews/application_interviews_edit', $data);
    }

    /**
     * Update interview session
     */
    public function updateSession($sessionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview session updated successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Delete interview session
     */
    public function deleteSession($sessionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview session deleted successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * List positions in interview session
     */
    public function sessionPositions($sessionId)
    {
        $session = $this->getMockInterviewSession($sessionId);
        $exercise = $this->getMockExercise($session['exercise_id']);
        $positions = $this->getMockSessionPositions($sessionId);
        $availablePositions = $this->getMockAvailablePositions($session['exercise_id']);

        $data = [
            'title' => 'Session Positions - ' . $session['session_name'],
            'session' => $session,
            'exercise' => $exercise,
            'positions' => $positions,
            'available_positions' => $availablePositions
        ];

        return view('application_interviews/application_interviews_positions', $data);
    }

    /**
     * Add position to interview session
     */
    public function addPositionToSession($sessionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Position added to interview session successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Remove position from interview session
     */
    public function removePositionFromSession($sessionId, $positionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Position removed from interview session successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Show interview settings for session
     */
    public function sessionSettings($sessionId)
    {
        $session = $this->getMockInterviewSession($sessionId);
        $exercise = $this->getMockExercise($session['exercise_id']);
        $settings = $this->getMockInterviewSettings($sessionId);

        $data = [
            'title' => 'Interview Settings - ' . $session['session_name'],
            'session' => $session,
            'exercise' => $exercise,
            'settings' => $settings
        ];

        return view('application_interviews/application_interviews_settings', $data);
    }

    /**
     * Save interview settings
     */
    public function saveSessionSettings($sessionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview settings saved successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Show interview schedule for session
     */
    public function sessionSchedule($sessionId)
    {
        $session = $this->getMockInterviewSession($sessionId);
        $exercise = $this->getMockExercise($session['exercise_id']);
        $schedule = $this->getMockInterviewSchedule($sessionId);

        $data = [
            'title' => 'Interview Schedule - ' . $session['session_name'],
            'session' => $session,
            'exercise' => $exercise,
            'schedule' => $schedule
        ];

        return view('application_interviews/application_interviews_schedule', $data);
    }

    /**
     * Generate interview schedule
     */
    public function generateSchedule($sessionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview schedule generated successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Update interview schedule
     */
    public function updateSchedule($sessionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview schedule updated successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * List interviewers in interview session
     */
    public function sessionInterviewers($sessionId)
    {
        $session = $this->getMockInterviewSession($sessionId);
        $exercise = $this->getMockExercise($session['exercise_id']);
        $interviewers = $this->getMockSessionInterviewers($sessionId);
        $availableInterviewers = $this->getMockAvailableInterviewers();

        $data = [
            'title' => 'Session Interviewers - ' . $session['session_name'],
            'session' => $session,
            'exercise' => $exercise,
            'interviewers' => $interviewers,
            'available_interviewers' => $availableInterviewers
        ];

        return view('application_interviews/application_interviews_interviewers', $data);
    }

    /**
     * Add interviewer to interview session
     */
    public function addInterviewerToSession($sessionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interviewer added to session successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Remove interviewer from interview session
     */
    public function removeInterviewerFromSession($sessionId, $interviewerId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interviewer removed from session successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Update interviewer role in session
     */
    public function updateSessionInterviewer($sessionId, $interviewerId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interviewer role updated successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Manage interview questions for position
     */
    public function positionQuestions($positionId)
    {
        $position = $this->getMockPosition($positionId);
        $questions = $this->getMockInterviewQuestions($positionId);

        $data = [
            'title' => 'Interview Questions - ' . $position['designation'],
            'position' => $position,
            'questions' => $questions
        ];

        return view('application_interviews/application_interviews_questions', $data);
    }

    /**
     * Save interview questions
     */
    public function saveQuestions($positionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview questions saved successfully'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * List interviewees for position
     */
    public function positionInterviewees($positionId)
    {
        $position = $this->getMockPosition($positionId);
        $interviewees = $this->getMockInterviewees($positionId);

        $data = [
            'title' => 'Interviewees - ' . $position['designation'],
            'position' => $position,
            'interviewees' => $interviewees
        ];

        return view('application_interviews/application_interviews_interviewees', $data);
    }

    /**
     * Show interview scoring for position
     */
    public function positionScoring($positionId)
    {
        $position = $this->getMockPosition($positionId);
        $interviewees = $this->getMockInterviewees($positionId);
        $scoringCriteria = $this->getMockScoringCriteria();

        $data = [
            'title' => 'Interview Scoring - ' . $position['designation'],
            'position' => $position,
            'interviewees' => $interviewees,
            'scoring_criteria' => $scoringCriteria
        ];

        return view('application_interviews/application_interviews_scoring', $data);
    }

    /**
     * Save interview scores
     */
    public function saveScoring($positionId)
    {
        // Mock response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview scores saved successfully'
        ];

        return $this->response->setJSON($response);
    }

    // Mock data methods

    /**
     * Get mock exercises with selection status
     */
    private function getMockExercises()
    {
        return [
            [
                'id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-001',
                'advertisement_no' => 'ADV-2024-001',
                'status' => 'selection',
                'publish_date_from' => '2024-01-25',
                'publish_date_to' => '2024-12-31',
                'description' => 'Recruitment exercise for IT positions',
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'exercise_name' => 'Finance Department Recruitment',
                'gazzetted_no' => 'GAZ-2024-002',
                'advertisement_no' => 'ADV-2024-002',
                'status' => 'selection',
                'publish_date_from' => '2024-01-26',
                'publish_date_to' => '2024-11-30',
                'description' => 'Recruitment exercise for finance positions',
                'created_at' => '2024-01-09 11:15:00'
            ],
            [
                'id' => 3,
                'exercise_name' => 'Healthcare Professionals Recruitment',
                'gazzetted_no' => 'GAZ-2024-003',
                'advertisement_no' => 'ADV-2024-003',
                'status' => 'selection',
                'publish_date_from' => '2024-02-01',
                'publish_date_to' => '2024-10-31',
                'description' => 'Recruitment exercise for healthcare positions',
                'created_at' => '2024-01-15 14:30:00'
            ]
        ];
    }

    /**
     * Get mock exercise by ID
     */
    private function getMockExercise($exerciseId)
    {
        $exercises = $this->getMockExercises();
        foreach ($exercises as $exercise) {
            if ($exercise['id'] == $exerciseId) {
                return $exercise;
            }
        }

        // Return first exercise if not found
        return $exercises[0];
    }

    /**
     * Get mock interview sessions for exercise
     */
    private function getMockInterviewSessions($exerciseId)
    {
        return [
            [
                'id' => 1,
                'exercise_id' => $exerciseId,
                'session_name' => 'Technical Interview Session',
                'session_description' => 'Technical interviews for IT positions',
                'start_date' => '2024-03-01',
                'end_date' => '2024-03-15',
                'status' => 'active',
                'created_at' => '2024-02-15 10:00:00',
                'positions_count' => 5
            ],
            [
                'id' => 2,
                'exercise_id' => $exerciseId,
                'session_name' => 'Management Interview Session',
                'session_description' => 'Management interviews for senior positions',
                'start_date' => '2024-03-16',
                'end_date' => '2024-03-30',
                'status' => 'draft',
                'created_at' => '2024-02-16 11:00:00',
                'positions_count' => 3
            ]
        ];
    }

    /**
     * Get mock interview session by ID
     */
    private function getMockInterviewSession($sessionId)
    {
        return [
            'id' => $sessionId,
            'exercise_id' => 1,
            'session_name' => 'Technical Interview Session',
            'session_description' => 'Technical interviews for IT positions',
            'start_date' => '2024-03-01',
            'end_date' => '2024-03-15',
            'status' => 'active',
            'created_at' => '2024-02-15 10:00:00'
        ];
    }

    /**
     * Get mock positions in interview session
     */
    private function getMockSessionPositions($sessionId)
    {
        return [
            [
                'id' => 1,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Developer',
                'classification' => 'Grade 12',
                'location' => 'Port Moresby',
                'annual_salary' => 'K80,000 - K100,000',
                'group_name' => 'Information Technology',
                'applicants_count' => 25,
                'shortlisted_count' => 8
            ],
            [
                'id' => 2,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'classification' => 'Grade 11',
                'location' => 'Lae',
                'annual_salary' => 'K70,000 - K85,000',
                'group_name' => 'Information Technology',
                'applicants_count' => 18,
                'shortlisted_count' => 6
            ]
        ];
    }

    /**
     * Get mock available positions for exercise
     */
    private function getMockAvailablePositions($exerciseId)
    {
        return [
            [
                'id' => 3,
                'position_reference' => 'IT-003',
                'designation' => 'System Analyst',
                'classification' => 'Grade 10',
                'location' => 'Mount Hagen',
                'group_name' => 'Information Technology'
            ],
            [
                'id' => 4,
                'position_reference' => 'IT-004',
                'designation' => 'Network Administrator',
                'classification' => 'Grade 9',
                'location' => 'Madang',
                'group_name' => 'Information Technology'
            ]
        ];
    }

    /**
     * Get mock position by ID
     */
    private function getMockPosition($positionId)
    {
        return [
            'id' => $positionId,
            'position_reference' => 'IT-001',
            'designation' => 'Senior Software Developer',
            'classification' => 'Grade 12',
            'location' => 'Port Moresby',
            'annual_salary' => 'K80,000 - K100,000',
            'qualifications' => 'Bachelor\'s degree in Computer Science or related field',
            'knowledge' => 'Programming languages, software development methodologies',
            'skills_competencies' => 'Problem solving, team leadership, communication',
            'job_experiences' => 'Minimum 5 years software development experience',
            'group_name' => 'Information Technology'
        ];
    }

    /**
     * Get mock interview settings
     */
    private function getMockInterviewSettings($sessionId)
    {
        return [
            'session_id' => $sessionId,
            'interview_duration' => 60,
            'break_duration' => 15,
            'start_time' => '09:00',
            'end_time' => '17:00',
            'interview_type' => 'panel',
            'panel_size' => 3,
            'scoring_method' => 'weighted',
            'notification_enabled' => true,
            'reminder_days' => 3,
            'venue' => 'Conference Room A, Government Building',
            'special_instructions' => 'Candidates should bring original certificates and ID'
        ];
    }

    /**
     * Get mock interview schedule
     */
    private function getMockInterviewSchedule($sessionId)
    {
        return [
            [
                'id' => 1,
                'session_id' => $sessionId,
                'date' => '2024-03-01',
                'time_slot' => '09:00 - 10:00',
                'position' => 'Senior Software Developer',
                'candidate_name' => 'John Doe',
                'candidate_email' => '<EMAIL>',
                'status' => 'scheduled',
                'panel_members' => 'Dr. Smith, Ms. Johnson, Mr. Brown'
            ],
            [
                'id' => 2,
                'session_id' => $sessionId,
                'date' => '2024-03-01',
                'time_slot' => '10:15 - 11:15',
                'position' => 'Senior Software Developer',
                'candidate_name' => 'Jane Smith',
                'candidate_email' => '<EMAIL>',
                'status' => 'scheduled',
                'panel_members' => 'Dr. Smith, Ms. Johnson, Mr. Brown'
            ],
            [
                'id' => 3,
                'session_id' => $sessionId,
                'date' => '2024-03-01',
                'time_slot' => '11:30 - 12:30',
                'position' => 'Database Administrator',
                'candidate_name' => 'Mike Wilson',
                'candidate_email' => '<EMAIL>',
                'status' => 'completed',
                'panel_members' => 'Dr. Smith, Ms. Johnson, Mr. Brown'
            ]
        ];
    }

    /**
     * Get mock interview questions
     */
    private function getMockInterviewQuestions($positionId)
    {
        return [
            [
                'id' => 1,
                'position_id' => $positionId,
                'category' => 'Technical',
                'question' => 'Explain the difference between object-oriented and functional programming paradigms.',
                'weight' => 25,
                'expected_duration' => 10
            ],
            [
                'id' => 2,
                'position_id' => $positionId,
                'category' => 'Technical',
                'question' => 'How would you optimize a slow-performing database query?',
                'weight' => 25,
                'expected_duration' => 10
            ],
            [
                'id' => 3,
                'position_id' => $positionId,
                'category' => 'Behavioral',
                'question' => 'Describe a challenging project you led and how you overcame obstacles.',
                'weight' => 25,
                'expected_duration' => 15
            ],
            [
                'id' => 4,
                'position_id' => $positionId,
                'category' => 'Situational',
                'question' => 'How would you handle a situation where a team member consistently misses deadlines?',
                'weight' => 25,
                'expected_duration' => 10
            ]
        ];
    }

    /**
     * Get mock interviewees
     */
    private function getMockInterviewees($positionId)
    {
        return [
            [
                'id' => 1,
                'position_id' => $positionId,
                'applicant_name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'interview_date' => '2024-03-01',
                'interview_time' => '09:00',
                'status' => 'scheduled',
                'score' => null,
                'remarks' => null
            ],
            [
                'id' => 2,
                'position_id' => $positionId,
                'applicant_name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'interview_date' => '2024-03-01',
                'interview_time' => '10:15',
                'status' => 'scheduled',
                'score' => null,
                'remarks' => null
            ],
            [
                'id' => 3,
                'position_id' => $positionId,
                'applicant_name' => 'Mike Wilson',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'interview_date' => '2024-03-01',
                'interview_time' => '11:30',
                'status' => 'completed',
                'score' => 85,
                'remarks' => 'Excellent technical knowledge and communication skills'
            ]
        ];
    }

    /**
     * Get mock scoring criteria
     */
    private function getMockScoringCriteria()
    {
        return [
            [
                'id' => 1,
                'criteria' => 'Technical Knowledge',
                'weight' => 40,
                'max_score' => 100
            ],
            [
                'id' => 2,
                'criteria' => 'Communication Skills',
                'weight' => 25,
                'max_score' => 100
            ],
            [
                'id' => 3,
                'criteria' => 'Problem Solving',
                'weight' => 20,
                'max_score' => 100
            ],
            [
                'id' => 4,
                'criteria' => 'Leadership Potential',
                'weight' => 15,
                'max_score' => 100
            ]
        ];
    }

    /**
     * Get mock session interviewers
     */
    private function getMockSessionInterviewers($sessionId)
    {
        return [
            [
                'id' => 1,
                'session_id' => $sessionId,
                'user_id' => 1,
                'name' => 'Dr. Sarah Johnson',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'department' => 'Information Technology',
                'position' => 'IT Director',
                'role' => 'Panel Chair',
                'expertise' => 'Technical Leadership, Software Development',
                'years_experience' => 15,
                'status' => 'active',
                'added_at' => '2024-02-15 10:00:00'
            ],
            [
                'id' => 2,
                'session_id' => $sessionId,
                'user_id' => 2,
                'name' => 'Mr. James Wilson',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'department' => 'Human Resources',
                'position' => 'HR Manager',
                'role' => 'Panel Member',
                'expertise' => 'Behavioral Assessment, HR Policies',
                'years_experience' => 12,
                'status' => 'active',
                'added_at' => '2024-02-15 11:00:00'
            ],
            [
                'id' => 3,
                'session_id' => $sessionId,
                'user_id' => 3,
                'name' => 'Ms. Maria Santos',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'department' => 'Information Technology',
                'position' => 'Senior Systems Analyst',
                'role' => 'Technical Assessor',
                'expertise' => 'Systems Analysis, Database Management',
                'years_experience' => 10,
                'status' => 'active',
                'added_at' => '2024-02-15 12:00:00'
            ]
        ];
    }

    /**
     * Get mock available interviewers
     */
    private function getMockAvailableInterviewers()
    {
        return [
            [
                'id' => 4,
                'name' => 'Dr. Michael Brown',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'department' => 'Finance',
                'position' => 'Finance Director',
                'expertise' => 'Financial Management, Budgeting',
                'years_experience' => 18
            ],
            [
                'id' => 5,
                'name' => 'Ms. Lisa Chen',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'department' => 'Information Technology',
                'position' => 'Network Administrator',
                'expertise' => 'Network Security, Infrastructure',
                'years_experience' => 8
            ],
            [
                'id' => 6,
                'name' => 'Mr. David Miller',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'department' => 'Administration',
                'position' => 'Deputy Secretary',
                'expertise' => 'Public Administration, Policy Development',
                'years_experience' => 20
            ],
            [
                'id' => 7,
                'name' => 'Dr. Patricia Lee',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'department' => 'Legal',
                'position' => 'Legal Advisor',
                'expertise' => 'Legal Compliance, Contract Law',
                'years_experience' => 14
            ]
        ];
    }
}
