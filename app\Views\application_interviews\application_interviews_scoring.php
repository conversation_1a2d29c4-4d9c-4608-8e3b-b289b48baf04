<?php
/**
 * Interview Scoring
 * Score interviews for a specific position
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/1') ?>">Sessions</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/session/1/positions') ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Scoring - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Scoring</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-briefcase me-1"></i>
                        <?= esc($position['designation']) ?> (<?= esc($position['position_reference']) ?>)
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/session/1/positions') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Positions
                    </a>
                    <button type="button" class="btn btn-success" id="exportScoresBtn">
                        <i class="fas fa-download me-2"></i>Export Scores
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scoring Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0"><?= count($interviewees) ?></h5>
                    <small class="text-muted">Total Interviewees</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0"><?= count(array_filter($interviewees, function($i) { return $i['status'] === 'completed'; })) ?></h5>
                    <small class="text-muted">Completed</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-star text-warning mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0"><?= count(array_filter($interviewees, function($i) { return !is_null($i['score']); })) ?></h5>
                    <small class="text-muted">Scored</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-0 bg-light">
                <div class="card-body">
                    <i class="fas fa-trophy text-info mb-2" style="font-size: 2rem;"></i>
                    <h5 class="mb-0">
                        <?php 
                        $scores = array_filter(array_column($interviewees, 'score'));
                        echo !empty($scores) ? round(array_sum($scores) / count($scores), 1) : '0';
                        ?>
                    </h5>
                    <small class="text-muted">Average Score</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Scoring Criteria -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0">
                <i class="fas fa-star me-2"></i>Scoring Criteria
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <?php foreach ($scoring_criteria as $criteria): ?>
                    <div class="col-md-3 mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-medium"><?= esc($criteria['criteria']) ?></span>
                            <span class="badge bg-info text-dark"><?= $criteria['weight'] ?>%</span>
                        </div>
                        <div class="progress mt-1" style="height: 4px;">
                            <div class="progress-bar bg-info" style="width: <?= $criteria['weight'] ?>%"></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Interviewees Scoring -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        Interviewees Scoring
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm active" data-filter="all">
                            All
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="completed">
                            Completed
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="scored">
                            Scored
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="pending">
                            Pending
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($interviewees)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Interviewees</h5>
                    <p class="text-muted">No interviewees have been scheduled for this position yet.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="intervieweesTable">
                        <thead class="table-light">
                            <tr>
                                <th>Candidate</th>
                                <th>Interview Details</th>
                                <th>Status</th>
                                <th>Score</th>
                                <th>Remarks</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($interviewees as $interviewee): ?>
                                <tr data-status="<?= $interviewee['status'] ?>" data-scored="<?= !is_null($interviewee['score']) ? 'true' : 'false' ?>">
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= esc($interviewee['applicant_name']) ?></div>
                                            <small class="text-muted">
                                                <i class="fas fa-envelope me-1"></i>
                                                <?= esc($interviewee['email']) ?>
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-phone me-1"></i>
                                                <?= esc($interviewee['phone']) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium">
                                                <?= date('M d, Y', strtotime($interviewee['interview_date'])) ?>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?= $interviewee['interview_time'] ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($interviewee['status'] === 'scheduled'): ?>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-calendar-check me-1"></i>Scheduled
                                            </span>
                                        <?php elseif ($interviewee['status'] === 'completed'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>Completed
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <?= ucfirst($interviewee['status']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!is_null($interviewee['score'])): ?>
                                            <div class="d-flex align-items-center">
                                                <span class="fw-bold me-2"><?= $interviewee['score'] ?>/100</span>
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar 
                                                        <?= $interviewee['score'] >= 80 ? 'bg-success' : 
                                                            ($interviewee['score'] >= 60 ? 'bg-warning' : 'bg-danger') ?>" 
                                                         style="width: <?= $interviewee['score'] ?>%"></div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Not scored</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($interviewee['remarks']): ?>
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?= esc($interviewee['remarks']) ?>">
                                                <?= esc($interviewee['remarks']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">No remarks</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button" 
                                                    class="btn btn-primary btn-sm score-btn" 
                                                    data-interviewee='<?= json_encode($interviewee) ?>'
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#scoreModal">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            <button type="button" 
                                                    class="btn btn-info btn-sm view-details-btn" 
                                                    data-interviewee='<?= json_encode($interviewee) ?>'
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#detailsModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Score Modal -->
<div class="modal fade" id="scoreModal" tabindex="-1" aria-labelledby="scoreModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="scoreModalLabel">
                    <i class="fas fa-star me-2"></i>Interview Scoring
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= form_open('interviews/position/' . $position['id'] . '/scoring/save', ['id' => 'scoreForm']) ?>
                    <input type="hidden" id="score_interviewee_id" name="interviewee_id">
                    
                    <div class="mb-3">
                        <h6 id="score_candidate_name" class="text-primary"></h6>
                        <p class="text-muted mb-0" id="score_interview_details"></p>
                    </div>
                    
                    <hr>
                    
                    <!-- Scoring Criteria -->
                    <div class="mb-4">
                        <h6 class="mb-3">Scoring Criteria</h6>
                        <?php foreach ($scoring_criteria as $criteria): ?>
                            <div class="mb-3">
                                <label class="form-label">
                                    <?= esc($criteria['criteria']) ?> 
                                    <span class="badge bg-info text-dark"><?= $criteria['weight'] ?>%</span>
                                </label>
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="range" 
                                               class="form-range" 
                                               id="criteria_<?= $criteria['id'] ?>" 
                                               name="criteria[<?= $criteria['id'] ?>]" 
                                               min="0" 
                                               max="<?= $criteria['max_score'] ?>" 
                                               value="0"
                                               oninput="updateScoreDisplay(<?= $criteria['id'] ?>, this.value)">
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="score_display_<?= $criteria['id'] ?>" 
                                                   min="0" 
                                                   max="<?= $criteria['max_score'] ?>" 
                                                   value="0"
                                                   onchange="updateScoreRange(<?= $criteria['id'] ?>, this.value)">
                                            <span class="input-group-text">/<?= $criteria['max_score'] ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Overall Score -->
                    <div class="mb-3">
                        <label class="form-label">Overall Score</label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="number" 
                                       class="form-control" 
                                       id="overall_score" 
                                       name="overall_score" 
                                       min="0" 
                                       max="100" 
                                       readonly>
                            </div>
                            <div class="col-md-6">
                                <div class="progress mt-2" style="height: 25px;">
                                    <div class="progress-bar" id="score_progress" style="width: 0%">0%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Remarks -->
                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" 
                                  id="remarks" 
                                  name="remarks" 
                                  rows="3"
                                  placeholder="Enter your comments about the candidate's performance..."></textarea>
                    </div>
                <?= form_close() ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="scoreForm" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save Score
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="fas fa-eye me-2"></i>Interview Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label fw-bold">Candidate Name</label>
                        <p class="form-control-plaintext" id="details_candidate_name">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Email</label>
                        <p class="form-control-plaintext" id="details_email">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Phone</label>
                        <p class="form-control-plaintext" id="details_phone">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Interview Date</label>
                        <p class="form-control-plaintext" id="details_date">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Interview Time</label>
                        <p class="form-control-plaintext" id="details_time">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Status</label>
                        <p class="form-control-plaintext" id="details_status">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Score</label>
                        <p class="form-control-plaintext" id="details_score">-</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">Remarks</label>
                        <p class="form-control-plaintext" id="details_remarks">-</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Scoring criteria weights for calculation
const criteriaWeights = <?= json_encode(array_column($scoring_criteria, 'weight', 'id')) ?>;

function updateScoreDisplay(criteriaId, value) {
    document.getElementById(`score_display_${criteriaId}`).value = value;
    calculateOverallScore();
}

function updateScoreRange(criteriaId, value) {
    document.getElementById(`criteria_${criteriaId}`).value = value;
    calculateOverallScore();
}

function calculateOverallScore() {
    let totalWeightedScore = 0;
    let totalWeight = 0;
    
    Object.keys(criteriaWeights).forEach(criteriaId => {
        const score = parseInt(document.getElementById(`criteria_${criteriaId}`).value) || 0;
        const weight = criteriaWeights[criteriaId];
        totalWeightedScore += (score * weight);
        totalWeight += weight;
    });
    
    const overallScore = totalWeight > 0 ? Math.round(totalWeightedScore / totalWeight) : 0;
    
    document.getElementById('overall_score').value = overallScore;
    
    const progressBar = document.getElementById('score_progress');
    progressBar.style.width = overallScore + '%';
    progressBar.textContent = overallScore + '%';
    
    // Update progress bar color based on score
    progressBar.className = 'progress-bar ' + 
        (overallScore >= 80 ? 'bg-success' : 
         overallScore >= 60 ? 'bg-warning' : 'bg-danger');
}

document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter table rows
            const rows = document.querySelectorAll('#intervieweesTable tbody tr');
            rows.forEach(row => {
                let show = false;
                
                if (filter === 'all') {
                    show = true;
                } else if (filter === 'completed') {
                    show = row.getAttribute('data-status') === 'completed';
                } else if (filter === 'scored') {
                    show = row.getAttribute('data-scored') === 'true';
                } else if (filter === 'pending') {
                    show = row.getAttribute('data-status') === 'scheduled' || row.getAttribute('data-scored') === 'false';
                }
                
                row.style.display = show ? '' : 'none';
            });
        });
    });

    // Score modal
    const scoreModal = document.getElementById('scoreModal');
    if (scoreModal) {
        scoreModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const interviewee = JSON.parse(button.getAttribute('data-interviewee'));
            
            document.getElementById('score_interviewee_id').value = interviewee.id;
            document.getElementById('score_candidate_name').textContent = interviewee.applicant_name;
            document.getElementById('score_interview_details').textContent = 
                `Interview: ${new Date(interviewee.interview_date).toLocaleDateString()} at ${interviewee.interview_time}`;
            
            // Pre-fill existing score if available
            if (interviewee.score) {
                document.getElementById('overall_score').value = interviewee.score;
            }
            if (interviewee.remarks) {
                document.getElementById('remarks').value = interviewee.remarks;
            }
        });
    }

    // Details modal
    const detailsModal = document.getElementById('detailsModal');
    if (detailsModal) {
        detailsModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const interviewee = JSON.parse(button.getAttribute('data-interviewee'));
            
            document.getElementById('details_candidate_name').textContent = interviewee.applicant_name;
            document.getElementById('details_email').textContent = interviewee.email;
            document.getElementById('details_phone').textContent = interviewee.phone;
            document.getElementById('details_date').textContent = new Date(interviewee.interview_date).toLocaleDateString();
            document.getElementById('details_time').textContent = interviewee.interview_time;
            document.getElementById('details_status').textContent = interviewee.status.charAt(0).toUpperCase() + interviewee.status.slice(1);
            document.getElementById('details_score').textContent = interviewee.score ? `${interviewee.score}/100` : 'Not scored';
            document.getElementById('details_remarks').textContent = interviewee.remarks || 'No remarks';
        });
    }

    // Score form submission
    const scoreForm = document.getElementById('scoreForm');
    if (scoreForm) {
        scoreForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    const modal = bootstrap.Modal.getInstance(scoreModal);
                    modal.hide();
                    window.location.reload();
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toastr !== 'undefined') {
                    toastr.error('An error occurred while saving the score');
                } else {
                    alert('An error occurred while saving the score');
                }
            });
        });
    }

    // Export scores
    document.getElementById('exportScoresBtn').addEventListener('click', function() {
        // Mock export functionality
        if (typeof toastr !== 'undefined') {
            toastr.info('Export functionality will be implemented');
        } else {
            alert('Export functionality will be implemented');
        }
    });
});
</script>
<?= $this->endSection() ?>
