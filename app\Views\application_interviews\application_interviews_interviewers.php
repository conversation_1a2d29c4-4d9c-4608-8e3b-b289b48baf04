<?php
/**
 * Interview Session Interviewers
 * Manage interviewers within an interview session
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Interviewers - <?= esc($session['session_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Session Interviewers</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-calendar-alt me-1"></i>
                        <?= esc($session['session_name']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Sessions
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInterviewerModal">
                        <i class="fas fa-plus me-2"></i>Add Interviewer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Session Info Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-calendar-alt text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Session Period</h6>
                        <small class="text-muted">
                            <?= date('M d, Y', strtotime($session['start_date'])) ?><br>
                            to <?= date('M d, Y', strtotime($session['end_date'])) ?>
                        </small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-users text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Interviewers</h6>
                        <span class="fw-bold"><?= count($interviewers) ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-user-tie text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Panel Chairs</h6>
                        <span class="fw-bold"><?= count(array_filter($interviewers, function($i) { return $i['role'] === 'Panel Chair'; })) ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-check-circle text-info mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Active</h6>
                        <span class="fw-bold"><?= count(array_filter($interviewers, function($i) { return $i['status'] === 'active'; })) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interviewers List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        Interview Panel Members
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm active" data-filter="all">
                            All
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="Panel Chair">
                            Panel Chairs
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="Panel Member">
                            Panel Members
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="Technical Assessor">
                            Technical Assessors
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($interviewers)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Interviewers Added</h5>
                    <p class="text-muted">Add interviewers to this session to form the interview panel.</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInterviewerModal">
                        <i class="fas fa-plus me-2"></i>Add First Interviewer
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="interviewersTable">
                        <thead class="table-light">
                            <tr>
                                <th>Interviewer Details</th>
                                <th>Position & Department</th>
                                <th>Contact Information</th>
                                <th>Role</th>
                                <th>Experience</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($interviewers as $interviewer): ?>
                                <tr class="interviewer-item" data-role="<?= $interviewer['role'] ?>">
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?= esc($interviewer['name']) ?></h6>
                                            <small class="text-muted">
                                                <strong>Expertise:</strong> <?= esc($interviewer['expertise']) ?>
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                Added: <?= date('M d, Y', strtotime($interviewer['added_at'])) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= esc($interviewer['position']) ?></div>
                                            <small class="text-muted">
                                                <i class="fas fa-building me-1"></i>
                                                <?= esc($interviewer['department']) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <small class="d-block">
                                                <i class="fas fa-envelope me-1"></i>
                                                <?= esc($interviewer['email']) ?>
                                            </small>
                                            <small class="d-block">
                                                <i class="fas fa-phone me-1"></i>
                                                <?= esc($interviewer['phone']) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($interviewer['role'] === 'Panel Chair'): ?>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-crown me-1"></i><?= $interviewer['role'] ?>
                                            </span>
                                        <?php elseif ($interviewer['role'] === 'Technical Assessor'): ?>
                                            <span class="badge bg-info text-dark">
                                                <i class="fas fa-cogs me-1"></i><?= $interviewer['role'] ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-user me-1"></i><?= $interviewer['role'] ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="fw-medium"><?= $interviewer['years_experience'] ?> years</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button"
                                                    class="btn btn-outline-info btn-sm view-details-btn"
                                                    data-interviewer='<?= json_encode($interviewer) ?>'
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#detailsModal"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-outline-primary btn-sm edit-role-btn"
                                                    data-interviewer='<?= json_encode($interviewer) ?>'
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#editRoleModal"
                                                    title="Edit Role">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-outline-danger btn-sm remove-interviewer-btn"
                                                    data-interviewer-id="<?= $interviewer['id'] ?>"
                                                    data-interviewer-name="<?= esc($interviewer['name']) ?>"
                                                    title="Remove Interviewer">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary Statistics -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <strong>Total Interviewers:</strong> <?= count($interviewers) ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Panel Chairs:</strong> <?= count(array_filter($interviewers, function($i) { return $i['role'] === 'Panel Chair'; })) ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Panel Members:</strong> <?= count(array_filter($interviewers, function($i) { return $i['role'] === 'Panel Member'; })) ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Technical Assessors:</strong> <?= count(array_filter($interviewers, function($i) { return $i['role'] === 'Technical Assessor'; })) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Interviewer Modal -->
<div class="modal fade" id="addInterviewerModal" tabindex="-1" aria-labelledby="addInterviewerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addInterviewerModalLabel">
                    <i class="fas fa-plus me-2"></i>Add Interviewer to Session
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (empty($available_interviewers)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5 class="text-muted">No Available Interviewers</h5>
                        <p class="text-muted">All available staff members have been added to this session.</p>
                    </div>
                <?php else: ?>
                    <?= form_open('interviews/session/' . $session['id'] . '/add-interviewer', ['id' => 'addInterviewerForm']) ?>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="interviewer_id" class="form-label">Select Interviewer <span class="text-danger">*</span></label>
                                <select class="form-select" id="interviewer_id" name="interviewer_id" required>
                                    <option value="">Choose an interviewer...</option>
                                    <?php foreach ($available_interviewers as $interviewer): ?>
                                        <option value="<?= $interviewer['id'] ?>"
                                                data-interviewer='<?= json_encode($interviewer) ?>'>
                                            <?= esc($interviewer['name']) ?> - <?= esc($interviewer['position']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="role" class="form-label">Interview Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="Panel Chair">Panel Chair</option>
                                    <option value="Panel Member">Panel Member</option>
                                    <option value="Technical Assessor">Technical Assessor</option>
                                    <option value="Observer">Observer</option>
                                </select>
                            </div>
                        </div>

                        <!-- Interviewer Details Preview -->
                        <div id="interviewerPreview" class="mt-4" style="display: none;">
                            <hr>
                            <h6 class="text-primary">Interviewer Details</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Name:</strong> <span id="preview_name">-</span><br>
                                    <strong>Position:</strong> <span id="preview_position">-</span><br>
                                    <strong>Department:</strong> <span id="preview_department">-</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Email:</strong> <span id="preview_email">-</span><br>
                                    <strong>Phone:</strong> <span id="preview_phone">-</span><br>
                                    <strong>Experience:</strong> <span id="preview_experience">-</span> years
                                </div>
                            </div>
                            <div class="mt-2">
                                <strong>Expertise:</strong> <span id="preview_expertise">-</span>
                            </div>
                        </div>
                    <?= form_close() ?>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <?php if (!empty($available_interviewers)): ?>
                    <button type="submit" form="addInterviewerForm" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Interviewer
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Edit Role Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1" aria-labelledby="editRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editRoleModalLabel">
                    <i class="fas fa-edit me-2"></i>Edit Interviewer Role
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= form_open('interviews/session/' . $session['id'] . '/update-interviewer/1', ['id' => 'editRoleForm']) ?>
                    <input type="hidden" id="edit_interviewer_id" name="interviewer_id">

                    <div class="mb-3">
                        <label class="form-label fw-bold">Interviewer</label>
                        <p class="form-control-plaintext" id="edit_interviewer_name">-</p>
                    </div>

                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Interview Role <span class="text-danger">*</span></label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="Panel Chair">Panel Chair</option>
                            <option value="Panel Member">Panel Member</option>
                            <option value="Technical Assessor">Technical Assessor</option>
                            <option value="Observer">Observer</option>
                        </select>
                    </div>
                <?= form_close() ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editRoleForm" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Update Role
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="fas fa-user me-2"></i>Interviewer Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Name</label>
                        <p class="form-control-plaintext" id="details_name">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Interview Role</label>
                        <p class="form-control-plaintext" id="details_role">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Position</label>
                        <p class="form-control-plaintext" id="details_position">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Department</label>
                        <p class="form-control-plaintext" id="details_department">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Email</label>
                        <p class="form-control-plaintext" id="details_email">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Phone</label>
                        <p class="form-control-plaintext" id="details_phone">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Years of Experience</label>
                        <p class="form-control-plaintext" id="details_experience">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Added to Session</label>
                        <p class="form-control-plaintext" id="details_added">-</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">Areas of Expertise</label>
                        <p class="form-control-plaintext" id="details_expertise">-</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Update active button
            document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter table rows
            const rows = document.querySelectorAll('#interviewersTable tbody .interviewer-item');
            rows.forEach(row => {
                if (filter === 'all' || row.getAttribute('data-role') === filter) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });

    // Interviewer selection preview
    const interviewerSelect = document.getElementById('interviewer_id');
    const interviewerPreview = document.getElementById('interviewerPreview');

    if (interviewerSelect) {
        interviewerSelect.addEventListener('change', function() {
            if (this.value) {
                const selectedOption = this.options[this.selectedIndex];
                const interviewer = JSON.parse(selectedOption.getAttribute('data-interviewer'));

                document.getElementById('preview_name').textContent = interviewer.name;
                document.getElementById('preview_position').textContent = interviewer.position;
                document.getElementById('preview_department').textContent = interviewer.department;
                document.getElementById('preview_email').textContent = interviewer.email;
                document.getElementById('preview_phone').textContent = interviewer.phone;
                document.getElementById('preview_experience').textContent = interviewer.years_experience;
                document.getElementById('preview_expertise').textContent = interviewer.expertise;

                interviewerPreview.style.display = 'block';
            } else {
                interviewerPreview.style.display = 'none';
            }
        });
    }

    // Add interviewer form
    const addInterviewerForm = document.getElementById('addInterviewerForm');
    if (addInterviewerForm) {
        addInterviewerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    } else {
                        alert(data.message);
                    }

                    const modal = bootstrap.Modal.getInstance(document.getElementById('addInterviewerModal'));
                    modal.hide();
                    window.location.reload();
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toastr !== 'undefined') {
                    toastr.error('An error occurred while adding the interviewer');
                } else {
                    alert('An error occurred while adding the interviewer');
                }
            });
        });
    }

    // Edit role modal
    const editRoleModal = document.getElementById('editRoleModal');
    if (editRoleModal) {
        editRoleModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const interviewer = JSON.parse(button.getAttribute('data-interviewer'));

            document.getElementById('edit_interviewer_id').value = interviewer.id;
            document.getElementById('edit_interviewer_name').textContent = interviewer.name;
            document.getElementById('edit_role').value = interviewer.role;

            // Update form action
            const form = document.getElementById('editRoleForm');
            form.action = `<?= base_url('interviews/session/' . $session['id'] . '/update-interviewer/') ?>${interviewer.id}`;
        });
    }

    // Edit role form
    const editRoleForm = document.getElementById('editRoleForm');
    if (editRoleForm) {
        editRoleForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    } else {
                        alert(data.message);
                    }

                    const modal = bootstrap.Modal.getInstance(editRoleModal);
                    modal.hide();
                    window.location.reload();
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toastr !== 'undefined') {
                    toastr.error('An error occurred while updating the role');
                } else {
                    alert('An error occurred while updating the role');
                }
            });
        });
    }

    // Details modal
    const detailsModal = document.getElementById('detailsModal');
    if (detailsModal) {
        detailsModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const interviewer = JSON.parse(button.getAttribute('data-interviewer'));

            document.getElementById('details_name').textContent = interviewer.name;
            document.getElementById('details_role').textContent = interviewer.role;
            document.getElementById('details_position').textContent = interviewer.position;
            document.getElementById('details_department').textContent = interviewer.department;
            document.getElementById('details_email').textContent = interviewer.email;
            document.getElementById('details_phone').textContent = interviewer.phone;
            document.getElementById('details_experience').textContent = interviewer.years_experience + ' years';
            document.getElementById('details_added').textContent = new Date(interviewer.added_at).toLocaleDateString();
            document.getElementById('details_expertise').textContent = interviewer.expertise;
        });
    }

    // Remove interviewer
    document.querySelectorAll('.remove-interviewer-btn').forEach(button => {
        button.addEventListener('click', function() {
            const interviewerId = this.getAttribute('data-interviewer-id');
            const interviewerName = this.getAttribute('data-interviewer-name');

            if (confirm(`Are you sure you want to remove "${interviewerName}" from this interview session?`)) {
                fetch(`<?= base_url('interviews/session/' . $session['id'] . '/remove-interviewer/') ?>${interviewerId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (typeof toastr !== 'undefined') {
                            toastr.success(data.message);
                        } else {
                            alert(data.message);
                        }
                        window.location.reload();
                    } else {
                        if (typeof toastr !== 'undefined') {
                            toastr.error(data.message);
                        } else {
                            alert('Error: ' + data.message);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (typeof toastr !== 'undefined') {
                        toastr.error('An error occurred while removing the interviewer');
                    } else {
                        alert('An error occurred while removing the interviewer');
                    }
                });
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
