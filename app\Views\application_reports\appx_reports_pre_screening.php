<?php
/**
 * View file for Pre-Screening Report
 *
 * @var array $position Position details
 * @var array $pre_screening_results List of pre-screening results
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Pre-Screening Report - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Pre-Screening Report</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?> - <?= esc($position['position_reference']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>
                        Export Excel
                    </button>
                    <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Group:</dt>
                                <dd class="col-sm-8"><?= esc($position['group_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pre-Screening Results -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Pre-Screening Results (<?= count($pre_screening_results) ?> Applications)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($pre_screening_results)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No pre-screening results found</h5>
                            <p class="text-muted">No applications have been pre-screened for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="preScreeningTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Application No.</th>
                                        <th>Full Name</th>
                                        <th>Gender</th>
                                        <th>Contact Details</th>
                                        <th>Criteria Assessment</th>
                                        <th>Score</th>
                                        <th>Status</th>
                                        <th>Remarks</th>
                                        <th>Screened By</th>
                                        <th>Date Screened</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pre_screening_results as $index => $result): ?>
                                        <?php 
                                        $contact = json_decode($result['contact_details'], true);
                                        $statusClass = $result['pre_screening_status'] === 'passed' ? 'success' : 'danger';
                                        ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong><?= esc($result['application_number']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($result['first_name'] . ' ' . $result['last_name']) ?></strong>
                                                </div>
                                            </td>
                                            <td><?= esc($result['gender']) ?></td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Phone:</strong> <?= esc($contact['phone'] ?? 'N/A') ?></div>
                                                    <div><strong>Email:</strong> <?= esc($contact['email'] ?? 'N/A') ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div>
                                                        <strong>Qualifications:</strong> 
                                                        <span class="badge bg-<?= $result['qualifications_met'] === 'Yes' ? 'success' : ($result['qualifications_met'] === 'Partial' ? 'warning' : 'danger') ?>">
                                                            <?= esc($result['qualifications_met']) ?>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <strong>Experience:</strong> 
                                                        <span class="badge bg-<?= $result['experience_met'] === 'Yes' ? 'success' : 'danger' ?>">
                                                            <?= esc($result['experience_met']) ?>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <strong>Documents:</strong> 
                                                        <span class="badge bg-<?= $result['documents_complete'] === 'Yes' ? 'success' : 'danger' ?>">
                                                            <?= esc($result['documents_complete']) ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <h5 class="mb-0 text-<?= $statusClass ?>">
                                                        <?= $result['pre_screening_score'] ?>%
                                                    </h5>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $statusClass ?>">
                                                    <i class="fas fa-<?= $result['pre_screening_status'] === 'passed' ? 'check' : 'times' ?> me-1"></i>
                                                    <?= ucfirst($result['pre_screening_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="small" style="max-width: 200px;">
                                                    <?= esc($result['pre_screening_remarks']) ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <?= esc($result['screened_by']) ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <?= date('M d, Y', strtotime($result['screened_at'])) ?>
                                                    <br>
                                                    <?= date('H:i', strtotime($result['screened_at'])) ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Pre-Screening Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($pre_screening_results) ?></h4>
                                <p class="mb-0">Total Screened</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">
                                    <?= count(array_filter($pre_screening_results, function($result) { return $result['pre_screening_status'] === 'passed'; })) ?>
                                </h4>
                                <p class="mb-0">Passed</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-danger">
                                    <?= count(array_filter($pre_screening_results, function($result) { return $result['pre_screening_status'] === 'failed'; })) ?>
                                </h4>
                                <p class="mb-0">Failed</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php 
                                $passedCount = count(array_filter($pre_screening_results, function($result) { return $result['pre_screening_status'] === 'passed'; }));
                                $passRate = count($pre_screening_results) > 0 ? round(($passedCount / count($pre_screening_results)) * 100, 1) : 0;
                                ?>
                                <h4 class="text-info"><?= $passRate ?>%</h4>
                                <p class="mb-0">Pass Rate</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // Mock export functionality for UI development
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show success message
        alert('Pre-screening report exported successfully!\n\nFile: pre_screening_report_' + new Date().toISOString().slice(0,10) + '.xlsx');
    }, 2000);
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $('#preScreeningTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[7, 'desc'], [6, 'desc']],
            columnDefs: [
                { orderable: false, targets: [4, 5, 8] }
            ]
        });
    }
});
</script>
<?= $this->endSection() ?>
