<?php
/**
 * View file for Form 3.7A Updated Profiling Report
 *
 * @var array $position Position details
 * @var array $updated_profiling_data List of updated profiling data with scores
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Form 3.7A Updated - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Form 3.7A Updated Profiling Report</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?> - <?= esc($position['position_reference']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>
                        Export Excel
                    </button>
                    <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Group:</dt>
                                <dd class="col-sm-8"><?= esc($position['group_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Updated Profiling Data -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        Form 3.7A Updated Candidate Profiles (<?= count($updated_profiling_data) ?> Candidates)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($updated_profiling_data)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No updated profiling data found</h5>
                            <p class="text-muted">No updated candidate profiles are available for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="updatedProfilingTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Rank</th>
                                        <th>Application No.</th>
                                        <th>Full Name</th>
                                        <th>Personal Info</th>
                                        <th>Contact Details</th>
                                        <th>Experience</th>
                                        <th>Pre-Screening</th>
                                        <th>Application Rating</th>
                                        <th>Interview</th>
                                        <th>Overall Score</th>
                                        <th>Selection Status</th>
                                        <th>Remarks</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($updated_profiling_data as $candidate): ?>
                                        <?php 
                                        $contact = json_decode($candidate['contact_details'], true);
                                        $selectionClass = $candidate['selection_status'] === 'selected' ? 'success' : 'secondary';
                                        ?>
                                        <tr>
                                            <td>
                                                <div class="text-center">
                                                    <span class="badge bg-primary fs-6">
                                                        #<?= $candidate['final_ranking'] ?>
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <strong><?= esc($candidate['application_number']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($candidate['first_name'] . ' ' . $candidate['last_name']) ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Gender:</strong> <?= esc($candidate['gender']) ?></div>
                                                    <div><strong>DOB:</strong> <?= date('M d, Y', strtotime($candidate['date_of_birth'])) ?></div>
                                                    <div><strong>Origin:</strong> <?= esc($candidate['place_of_origin']) ?></div>
                                                    <div><strong>Marital:</strong> <?= esc($candidate['marital_status']) ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Phone:</strong> <?= esc($contact['phone'] ?? 'N/A') ?></div>
                                                    <div><strong>Email:</strong> <?= esc($contact['email'] ?? 'N/A') ?></div>
                                                    <div><strong>Address:</strong> <?= esc($candidate['location_address']) ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Current:</strong> <?= esc($candidate['current_position']) ?></div>
                                                    <div><strong>Employer:</strong> <?= esc($candidate['current_employer']) ?></div>
                                                    <div><strong>Salary:</strong> <?= esc($candidate['current_salary']) ?></div>
                                                    <div><strong>Experience:</strong> <?= $candidate['total_experience_years'] ?> years</div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <span class="badge bg-<?= $candidate['pre_screening_status'] === 'passed' ? 'success' : 'danger' ?>">
                                                        <?= ucfirst($candidate['pre_screening_status']) ?>
                                                    </span>
                                                    <div class="mt-1">
                                                        <strong><?= $candidate['pre_screening_score'] ?>%</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <div class="mb-1">
                                                        <span class="badge bg-info">Rated</span>
                                                    </div>
                                                    <div>
                                                        <strong><?= $candidate['application_rating_score'] ?>%</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <span class="badge bg-<?= $candidate['interview_status'] === 'interviewed' ? 'success' : 'secondary' ?>">
                                                        <?= ucfirst($candidate['interview_status']) ?>
                                                    </span>
                                                    <div class="mt-1">
                                                        <strong><?= $candidate['interview_score'] ?>%</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <h5 class="mb-0 text-primary">
                                                        <?= $candidate['overall_score'] ?>%
                                                    </h5>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $selectionClass ?>">
                                                    <i class="fas fa-<?= $candidate['selection_status'] === 'selected' ? 'check' : 'times' ?> me-1"></i>
                                                    <?= ucfirst($candidate['selection_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="small" style="max-width: 200px;">
                                                    <?= esc($candidate['remarks']) ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Updated Profiling Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($updated_profiling_data) ?></h4>
                                <p class="mb-0">Total Profiles</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-success">
                                    <?= count(array_filter($updated_profiling_data, function($candidate) { return $candidate['selection_status'] === 'selected'; })) ?>
                                </h4>
                                <p class="mb-0">Selected</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-warning">
                                    <?= count(array_filter($updated_profiling_data, function($candidate) { return $candidate['interview_status'] === 'interviewed'; })) ?>
                                </h4>
                                <p class="mb-0">Interviewed</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <?php 
                                $avgOverallScore = count($updated_profiling_data) > 0 ? 
                                    round(array_sum(array_column($updated_profiling_data, 'overall_score')) / count($updated_profiling_data), 1) : 0;
                                ?>
                                <h4 class="text-info"><?= $avgOverallScore ?>%</h4>
                                <p class="mb-0">Avg. Overall Score</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <?php 
                                $avgInterviewScore = count($updated_profiling_data) > 0 ? 
                                    round(array_sum(array_column($updated_profiling_data, 'interview_score')) / count($updated_profiling_data), 1) : 0;
                                ?>
                                <h4 class="text-secondary"><?= $avgInterviewScore ?>%</h4>
                                <p class="mb-0">Avg. Interview Score</p>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <?php 
                                $avgExperience = count($updated_profiling_data) > 0 ? 
                                    round(array_sum(array_column($updated_profiling_data, 'total_experience_years')) / count($updated_profiling_data), 1) : 0;
                                ?>
                                <h4 class="text-dark"><?= $avgExperience ?></h4>
                                <p class="mb-0">Avg. Experience (Years)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // Mock export functionality for UI development
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show success message
        alert('Form 3.7A updated profiling report exported successfully!\n\nFile: form_3_7a_updated_' + new Date().toISOString().slice(0,10) + '.xlsx');
    }, 2000);
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $('#updatedProfilingTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [3, 4, 11] }
            ]
        });
    }
});
</script>
<?= $this->endSection() ?>
