<?php
/**
 * View file for Form 3.7 Profiling Report
 *
 * @var array $position Position details
 * @var array $profiling_data List of profiling data
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Form 3.7 Profiling - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Form 3.7 Profiling Report</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?> - <?= esc($position['position_reference']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>
                        Export Excel
                    </button>
                    <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Group:</dt>
                                <dd class="col-sm-8"><?= esc($position['group_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profiling Data -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-check me-2"></i>
                        Form 3.7 Candidate Profiles (<?= count($profiling_data) ?> Candidates)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($profiling_data)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No profiling data found</h5>
                            <p class="text-muted">No candidate profiles are available for this position.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($profiling_data as $index => $candidate): ?>
                            <div class="card mb-4 border-secondary">
                                <div class="card-header bg-light">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h6 class="mb-0">
                                                <strong><?= esc($candidate['first_name'] . ' ' . $candidate['last_name']) ?></strong>
                                                <span class="text-muted ms-2">(<?= esc($candidate['application_number']) ?>)</span>
                                            </h6>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <span class="badge bg-<?= $candidate['pre_screening_status'] === 'passed' ? 'success' : 'danger' ?>">
                                                <?= ucfirst($candidate['pre_screening_status']) ?>
                                            </span>
                                            <?php if ($candidate['shortlist_status'] === 'shortlisted'): ?>
                                                <span class="badge bg-warning">Shortlisted</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Personal Information -->
                                        <div class="col-md-6">
                                            <h6 class="text-primary mb-3">Personal Information</h6>
                                            <dl class="row">
                                                <dt class="col-sm-5">Gender:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['gender']) ?></dd>
                                                <dt class="col-sm-5">Date of Birth:</dt>
                                                <dd class="col-sm-7"><?= date('M d, Y', strtotime($candidate['date_of_birth'])) ?></dd>
                                                <dt class="col-sm-5">Place of Origin:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['place_of_origin']) ?></dd>
                                                <dt class="col-sm-5">Citizenship:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['citizenship']) ?></dd>
                                                <dt class="col-sm-5">Marital Status:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['marital_status']) ?></dd>
                                            </dl>
                                        </div>

                                        <!-- Contact & Employment -->
                                        <div class="col-md-6">
                                            <h6 class="text-primary mb-3">Contact & Current Employment</h6>
                                            <?php $contact = json_decode($candidate['contact_details'], true); ?>
                                            <dl class="row">
                                                <dt class="col-sm-5">Phone:</dt>
                                                <dd class="col-sm-7"><?= esc($contact['phone'] ?? 'N/A') ?></dd>
                                                <dt class="col-sm-5">Email:</dt>
                                                <dd class="col-sm-7"><?= esc($contact['email'] ?? 'N/A') ?></dd>
                                                <dt class="col-sm-5">Address:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['location_address']) ?></dd>
                                                <dt class="col-sm-5">Current Employer:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['current_employer']) ?></dd>
                                                <dt class="col-sm-5">Current Position:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['current_position']) ?></dd>
                                                <dt class="col-sm-5">Current Salary:</dt>
                                                <dd class="col-sm-7"><?= esc($candidate['current_salary']) ?></dd>
                                            </dl>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="row">
                                        <!-- Qualifications -->
                                        <div class="col-md-6">
                                            <h6 class="text-success mb-3">Educational Qualifications</h6>
                                            <?php if (!empty($candidate['qualifications'])): ?>
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-bordered">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>Level</th>
                                                                <th>Field</th>
                                                                <th>Institution</th>
                                                                <th>Year</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php foreach ($candidate['qualifications'] as $qual): ?>
                                                                <tr>
                                                                    <td><?= esc($qual['level']) ?></td>
                                                                    <td><?= esc($qual['field']) ?></td>
                                                                    <td><?= esc($qual['institution']) ?></td>
                                                                    <td><?= esc($qual['year']) ?></td>
                                                                </tr>
                                                            <?php endforeach; ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php else: ?>
                                                <p class="text-muted">No qualifications data available</p>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Work Experience -->
                                        <div class="col-md-6">
                                            <h6 class="text-info mb-3">Work Experience (<?= $candidate['total_experience_years'] ?> years)</h6>
                                            <?php if (!empty($candidate['experiences'])): ?>
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-bordered">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>Employer</th>
                                                                <th>Position</th>
                                                                <th>Period</th>
                                                                <th>Years</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php foreach ($candidate['experiences'] as $exp): ?>
                                                                <tr>
                                                                    <td><?= esc($exp['employer']) ?></td>
                                                                    <td><?= esc($exp['position']) ?></td>
                                                                    <td><?= esc($exp['from']) ?> - <?= esc($exp['to']) ?></td>
                                                                    <td><?= esc($exp['years']) ?></td>
                                                                </tr>
                                                            <?php endforeach; ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php else: ?>
                                                <p class="text-muted">No experience data available</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Profiling Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($profiling_data) ?></h4>
                                <p class="mb-0">Total Profiles</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">
                                    <?= count(array_filter($profiling_data, function($candidate) { return $candidate['pre_screening_status'] === 'passed'; })) ?>
                                </h4>
                                <p class="mb-0">Pre-Screening Passed</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">
                                    <?= count(array_filter($profiling_data, function($candidate) { return $candidate['shortlist_status'] === 'shortlisted'; })) ?>
                                </h4>
                                <p class="mb-0">Shortlisted</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php 
                                $avgExperience = count($profiling_data) > 0 ? 
                                    round(array_sum(array_column($profiling_data, 'total_experience_years')) / count($profiling_data), 1) : 0;
                                ?>
                                <h4 class="text-info"><?= $avgExperience ?></h4>
                                <p class="mb-0">Avg. Experience (Years)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // Mock export functionality for UI development
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show success message
        alert('Form 3.7 profiling report exported successfully!\n\nFile: form_3_7_profiling_' + new Date().toISOString().slice(0,10) + '.xlsx');
    }, 2000);
}
</script>
<?= $this->endSection() ?>
