<?php

namespace App\Controllers;

use CodeIgniter\Controller;

/**
 * ReportsMainController
 * 
 * Main controller for reports system - handles exercise listing, dashboard, and navigation
 */
class ReportsMainController extends Controller
{
    public function __construct()
    {
        helper(['url', 'form']);
    }

    /**
     * [GET] Main reports index - redirects to exercises list
     * URI: /reports
     */
    public function index()
    {
        return redirect()->to('reports/exercises');
    }

    /**
     * [GET] Display exercises with selection status for reports
     * URI: /reports/exercises
     */
    public function exercises()
    {
        // Mock exercises data based on ExerciseModel structure - only selection status
        $exercises = [
            [
                'id' => 1,
                'org_id' => 1,
                'exercise_name' => 'IT Department Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-15',
                'advertisement_no' => 'ADV-2024-001',
                'advertisement_date' => '2024-01-20',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-25',
                'publish_date_to' => '2024-12-31',
                'description' => 'Recruitment exercise for various IT positions including software engineers, system administrators, and database specialists.',
                'status' => 'selection',
                'created_at' => '2024-01-10 09:00:00',
                'position_count' => 8,
                'application_count' => 156,
                'shortlisted_count' => 45
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-16',
                'advertisement_no' => 'ADV-2024-002',
                'advertisement_date' => '2024-01-21',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-26',
                'publish_date_to' => '2024-11-30',
                'description' => 'Recruitment exercise for finance positions including accountants, financial analysts, and budget officers.',
                'status' => 'selection',
                'created_at' => '2024-01-05 14:30:00',
                'position_count' => 6,
                'application_count' => 89,
                'shortlisted_count' => 28
            ],
            [
                'id' => 3,
                'org_id' => 1,
                'exercise_name' => 'Human Resources Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-003',
                'gazzetted_date' => '2024-01-17',
                'advertisement_no' => 'ADV-2024-003',
                'advertisement_date' => '2024-01-22',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-27',
                'publish_date_to' => '2024-10-31',
                'description' => 'Recruitment exercise for HR positions including HR officers, training coordinators, and recruitment specialists.',
                'status' => 'selection',
                'created_at' => '2024-01-03 11:15:00',
                'position_count' => 4,
                'application_count' => 67,
                'shortlisted_count' => 20
            ]
        ];

        $data = [
            'title' => 'Reports - Exercises',
            'menu' => 'reports',
            'exercises' => $exercises
        ];

        return view('application_reports/appx_reports_exercises', $data);
    }

    /**
     * [GET] Display reports dashboard for specific exercise
     * URI: /reports/dashboard/{exerciseId}
     */
    public function dashboard($exerciseId)
    {
        // Mock exercise data
        $exercise = $this->getMockExercise($exerciseId);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Mock dashboard statistics
        $statistics = [
            'total_applications' => 156,
            'pre_screened_passed' => 89,
            'pre_screened_failed' => 67,
            'shortlisted' => 45,
            'interviewed' => 32,
            'selected' => 12,
            'positions_filled' => 8,
            'positions_vacant' => 0
        ];

        $data = [
            'title' => 'Reports Dashboard - ' . $exercise['exercise_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'statistics' => $statistics
        ];

        return view('application_reports/appx_reports_dashboard', $data);
    }

    /**
     * [GET] Display positions for exercise reports
     * URI: /reports/positions/{exerciseId}
     */
    public function positions($exerciseId)
    {
        // Mock exercise data
        $exercise = $this->getMockExercise($exerciseId);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Mock positions data based on PositionsModel and PositionsGroupModel
        $positions = [
            [
                'id' => 1,
                'exercise_id' => $exerciseId,
                'position_group_id' => 1,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Engineer',
                'classification' => 'Grade 12',
                'award' => 'K85,000 - K95,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'application_count' => 45,
                'shortlisted_count' => 15,
                'interviewed_count' => 12,
                'selected_count' => 3
            ],
            [
                'id' => 2,
                'exercise_id' => $exerciseId,
                'position_group_id' => 1,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'classification' => 'Grade 11',
                'award' => 'K75,000 - K85,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'application_count' => 32,
                'shortlisted_count' => 10,
                'interviewed_count' => 8,
                'selected_count' => 2
            ],
            [
                'id' => 3,
                'exercise_id' => $exerciseId,
                'position_group_id' => 2,
                'position_reference' => 'SYS-001',
                'designation' => 'System Administrator',
                'classification' => 'Grade 10',
                'award' => 'K65,000 - K75,000',
                'location' => 'Port Moresby',
                'group_name' => 'System Administration',
                'application_count' => 28,
                'shortlisted_count' => 8,
                'interviewed_count' => 6,
                'selected_count' => 2
            ],
            [
                'id' => 4,
                'exercise_id' => $exerciseId,
                'position_group_id' => 2,
                'position_reference' => 'SYS-002',
                'designation' => 'Network Administrator',
                'classification' => 'Grade 10',
                'award' => 'K65,000 - K75,000',
                'location' => 'Lae',
                'group_name' => 'System Administration',
                'application_count' => 25,
                'shortlisted_count' => 7,
                'interviewed_count' => 5,
                'selected_count' => 2
            ]
        ];

        $data = [
            'title' => 'Reports - Positions',
            'menu' => 'reports',
            'exercise' => $exercise,
            'positions' => $positions
        ];

        return view('application_reports/appx_reports_positions', $data);
    }

    /**
     * Get mock exercise data
     */
    private function getMockExercise($exerciseId)
    {
        $exercises = [
            1 => [
                'id' => 1,
                'org_id' => 1,
                'exercise_name' => 'IT Department Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-15',
                'advertisement_no' => 'ADV-2024-001',
                'advertisement_date' => '2024-01-20',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-25',
                'publish_date_to' => '2024-12-31',
                'description' => 'Recruitment exercise for various IT positions including software engineers, system administrators, and database specialists.',
                'status' => 'selection',
                'created_at' => '2024-01-10 09:00:00'
            ],
            2 => [
                'id' => 2,
                'org_id' => 1,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-16',
                'advertisement_no' => 'ADV-2024-002',
                'advertisement_date' => '2024-01-21',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-26',
                'publish_date_to' => '2024-11-30',
                'description' => 'Recruitment exercise for finance positions including accountants, financial analysts, and budget officers.',
                'status' => 'selection',
                'created_at' => '2024-01-05 14:30:00'
            ],
            3 => [
                'id' => 3,
                'org_id' => 1,
                'exercise_name' => 'Human Resources Recruitment 2024',
                'gazzetted_no' => 'GAZ-2024-003',
                'gazzetted_date' => '2024-01-17',
                'advertisement_no' => 'ADV-2024-003',
                'advertisement_date' => '2024-01-22',
                'mode_of_advertisement' => 'Online & Print Media',
                'publish_date_from' => '2024-01-27',
                'publish_date_to' => '2024-10-31',
                'description' => 'Recruitment exercise for HR positions including HR officers, training coordinators, and recruitment specialists.',
                'status' => 'selection',
                'created_at' => '2024-01-03 11:15:00'
            ]
        ];

        return $exercises[$exerciseId] ?? null;
    }
}
