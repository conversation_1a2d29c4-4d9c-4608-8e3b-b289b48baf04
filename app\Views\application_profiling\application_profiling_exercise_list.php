<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-clipboard-list me-2"></i>Profiling Exercises</h2>
            <p class="text-muted">Selection exercises available for application profiling</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?= base_url('dashboard') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($exercises)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No exercises in selection phase found.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="exercisesTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="30%">Exercise Name</th>
                                <th width="15%">Advertisement No.</th>
                                <th width="15%">Published From</th>
                                <th width="15%">Published To</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($exercises as $exercise): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td><?= esc($exercise['exercise_name']) ?></td>
                                    <td><?= esc($exercise['advertisement_no']) ?></td>
                                    <td><?= date('d M Y', strtotime($exercise['publish_date_from'])) ?></td>
                                    <td><?= date('d M Y', strtotime($exercise['publish_date_to'])) ?></td>
                                    <td>
                                        <a href="<?= base_url('profile_applications_exercise/exercise/' . $exercise['id'] . '/positions') ?>"
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-briefcase"></i> View Positions
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#exercisesTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by ID asc
        language: {
            search: "Search exercises:",
            lengthMenu: "Show _MENU_ exercises per page",
            info: "Showing _START_ to _END_ of _TOTAL_ exercises",
            emptyTable: "No exercises available for profiling",
        }
    });
});
</script>
<?= $this->endSection() ?>
