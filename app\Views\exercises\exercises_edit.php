<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Edit Exercise</h5>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('exercises') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Exercise Management
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger">
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <?= form_open('exercises/update/' . $exercise['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                <input type="hidden" name="org_id" value="<?= $exercise['org_id'] ?>">

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Exercise Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="exercise_name"
                               value="<?= old('exercise_name', $exercise['exercise_name']) ?>" required>
                        <div class="invalid-feedback">
                            Please provide a valid exercise name.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Gazzetted No <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="gazzetted_no"
                               value="<?= old('gazzetted_no', $exercise['gazzetted_no']) ?>" required>
                        <div class="invalid-feedback">
                            Please provide a gazzetted number.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Gazzetted Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" name="gazzetted_date"
                               value="<?= old('gazzetted_date', $exercise['gazzetted_date']) ?>" required>
                        <div class="invalid-feedback">
                            Please provide a gazzetted date.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Advertisement No <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="advertisement_no"
                               value="<?= old('advertisement_no', $exercise['advertisement_no']) ?>" required>
                        <div class="invalid-feedback">
                            Please provide an advertisement number.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Advertisement Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" name="advertisement_date"
                               value="<?= old('advertisement_date', $exercise['advertisement_date']) ?>" required>
                        <div class="invalid-feedback">
                            Please provide an advertisement date.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Mode of Advertisement <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="mode_of_advertisement"
                               value="<?= old('mode_of_advertisement', $exercise['mode_of_advertisement']) ?>" required
                               placeholder="e.g., Newspaper, Online, TV">
                        <div class="invalid-feedback">
                            Please provide the mode of advertisement.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Publish Date From <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" name="publish_date_from"
                               value="<?= old('publish_date_from', $exercise['publish_date_from']) ?>" required>
                        <div class="invalid-feedback">
                            Please provide a publish start date.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Publish Date To <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" name="publish_date_to"
                               value="<?= old('publish_date_to', $exercise['publish_date_to']) ?>" required>
                        <div class="invalid-feedback">
                            Please provide a publish end date.
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"><?= old('description', $exercise['description']) ?></textarea>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Exercise
                        </button>
                        <a href="<?= base_url('exercises') ?>" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a href="<?= base_url('exercises/view/' . $exercise['id']) ?>" class="btn btn-info ms-2">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a>
                    </div>
                </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
<?= $this->endSection() ?>
