<?php
/**
 * View file for shortlisting detail form
 *
 * @var array $exercise Exercise details
 * @var array $position Position details
 * @var array $application Application details
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting') ?>">Shortlisting</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting/positions/' . $exercise['id']) ?>">
                            <?= esc($exercise['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>">
                            <?= esc($position['designation']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($application['first_name'] . ' ' . $application['last_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-user-check me-2"></i>Shortlist Application</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Applications
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Application Summary -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Application Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Application No:</strong></td>
                                    <td><?= esc($application['application_number']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Gender:</strong></td>
                                    <td><?= esc($application['gender']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Date of Birth:</strong></td>
                                    <td><?= date('M d, Y', strtotime($application['date_of_birth'])) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Citizenship:</strong></td>
                                    <td><?= esc($application['citizenship']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Current Employer:</strong></td>
                                    <td><?= esc($application['current_employer']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Current Position:</strong></td>
                                    <td><?= esc($application['current_position']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Rating:</strong></td>
                                    <td>
                                        <span class="badge bg-info fs-6">
                                            <?= $application['rating_total'] ?>/<?= $application['rating_max'] ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Applied Date:</strong></td>
                                    <td><?= date('M d, Y', strtotime($application['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Current Status:</strong></td>
                                    <td>
                                        <?php
                                        $statusClass = 'secondary';
                                        $statusText = 'Pending';
                                        
                                        switch($application['shortlist_status']) {
                                            case 'shortlisted':
                                                $statusClass = 'success';
                                                $statusText = 'Shortlisted';
                                                break;
                                            case 'eliminated':
                                                $statusClass = 'danger';
                                                $statusText = 'Eliminated';
                                                break;
                                            case 'withdrawn':
                                                $statusClass = 'warning';
                                                $statusText = 'Withdrawn';
                                                break;
                                        }
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Position Information -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>Position Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Position:</strong></td>
                            <td><?= esc($position['designation']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Reference:</strong></td>
                            <td><?= esc($position['reference']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Classification:</strong></td>
                            <td><?= esc($position['classification']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Location:</strong></td>
                            <td><?= esc($position['location']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Salary:</strong></td>
                            <td>K<?= number_format($position['annual_salary']) ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Shortlisting Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Shortlisting Decision</h5>
                </div>
                <div class="card-body">
                    <?= form_open(base_url('shortlisting/update/' . $application['id'])) ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shortlist_status" class="form-label">
                                        <strong>Shortlisting Status <span class="text-danger">*</span></strong>
                                    </label>
                                    <select name="shortlist_status" id="shortlist_status" class="form-select" required>
                                        <option value="">Select Status</option>
                                        <option value="shortlisted" <?= (old('shortlist_status') == 'shortlisted') ? 'selected' : '' ?>>
                                            Shortlisted
                                        </option>
                                        <option value="eliminated" <?= (old('shortlist_status') == 'eliminated') ? 'selected' : '' ?>>
                                            Eliminated
                                        </option>
                                        <option value="withdrawn" <?= (old('shortlist_status') == 'withdrawn') ? 'selected' : '' ?>>
                                            Withdrawn
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="remarks" class="form-label">
                                        <strong>Remarks</strong>
                                    </label>
                                    <textarea name="remarks" id="remarks" class="form-control" rows="3" 
                                              placeholder="Enter remarks or reason for decision..."><?= old('remarks') ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" 
                                       class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Save Decision
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form validation
    $('form').on('submit', function(e) {
        const status = $('#shortlist_status').val();
        const remarks = $('#remarks').val().trim();
        
        if (!status) {
            e.preventDefault();
            toastr.error('Please select a shortlisting status.');
            $('#shortlist_status').focus();
            return false;
        }
        
        if (status === 'eliminated' && !remarks) {
            e.preventDefault();
            toastr.error('Remarks are required when eliminating an application.');
            $('#remarks').focus();
            return false;
        }
        
        return true;
    });
    
    // Auto-focus on status field
    $('#shortlist_status').focus();
});
</script>
<?= $this->endSection() ?>
