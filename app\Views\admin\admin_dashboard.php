<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- Dashboard Content -->
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Welcome back, <?= $user_name ?>!</h2>
                    <p class="text-muted mb-0">Manage your recruitment activities and job postings.</p>
                </div>
                <div class="col-auto">
                    <span class="text-muted"><?= date('l, F j, Y') ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="row g-4 mb-4">
        <!-- Active Jobs Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Active Jobs</p>
                            <h3 class="fw-bold mb-3">24</h3>
                        </div>
                        <div class="bg-navy bg-opacity-10 rounded p-3">
                            <i class="fas fa-briefcase text-navy fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i>5
                        </span>
                        <span class="text-muted small">New this week</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--navy), #003B7B);"></div>
                </div>
            </div>
        </div>

        <!-- Total Applications Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Total Applications</p>
                            <h3 class="fw-bold mb-3">156</h3>
                        </div>
                        <div class="bg-accent-red bg-opacity-10 rounded p-3">
                            <i class="fas fa-file-alt text-accent-red fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i>12
                        </span>
                        <span class="text-muted small">New today</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--accent-red), var(--deep-red));"></div>
                </div>
            </div>
        </div>

        <!-- Shortlisted Candidates -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Shortlisted</p>
                            <h3 class="fw-bold mb-3">45</h3>
                        </div>
                        <div class="bg-lime bg-opacity-10 rounded p-3">
                            <i class="fas fa-user-check text-dark-green fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i>8
                        </span>
                        <span class="text-muted small">This week</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--lime), var(--dark-green));"></div>
                </div>
            </div>
        </div>

        <!-- Interviews Scheduled -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Interviews</p>
                            <h3 class="fw-bold mb-3">12</h3>
                        </div>
                        <div class="bg-info bg-opacity-10 rounded p-3">
                            <i class="fas fa-calendar-check text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small">Scheduled this week</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #0dcaf0, #0d6efd);"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Section -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title mb-4">Quick Access</h5>
            <div class="row g-4">
                <!-- Manage Exercises -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('exercises') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-tasks text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Manage Exercises</span>
                        </div>
                    </a>
                </div>

                <!-- Manage Position Groups -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('positions/positions_exercises') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-briefcase text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Manage Positions</span>
                        </div>
                    </a>
                </div>

                <!-- Incoming Applications -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('incoming_applications') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-file-alt text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Incoming Applications</span>
                        </div>
                    </a>
                </div>

                 <!-- Applications Pre-Screening -->
                 <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('application_pre_screening') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-clipboard-check text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Pre-Screening</span>
                        </div>
                    </a>
                </div>

                <!-- Applications Profiling -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('profile_applications_exercise') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-user-tag text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Profiling</span>
                        </div>
                    </a>
                </div>

                <!-- Scoring/Rating -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('rating') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-star text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Scoring/Rating</span>
                        </div>
                    </a>
                </div>

                <!-- Shortlist (Shortlist/Elimination) -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('shortlisting') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-list-check text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Shortlist (Shortlist/Elimination)</span>
                        </div>
                    </a>
                </div>

                <!-- Interviews -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('interviews') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-calendar-alt text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Interviews</span>
                        </div>
                    </a>
                </div>

                <!-- Reports -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('reports') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-chart-bar text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Reports</span>
                        </div>
                    </a>
                </div>

                <!-- Settings -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('settings') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-cog text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Settings</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Applications -->
    <div class="card mb-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Recent Applications</h5>
            <a href="<?= base_url('applications') ?>" class="btn btn-link text-accent-red text-decoration-none">View All</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Applicant</th>
                            <th>Position</th>
                            <th>Status</th>
                            <th>Applied Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="px-4">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                    <span class="fw-medium">John Doe</span>
                                </div>
                            </td>
                            <td>Software Engineer</td>
                            <td>
                                <span class="badge bg-warning bg-opacity-10 text-warning">Under Review</span>
                            </td>
                            <td>Aug 15, 2024</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">View</button>
                            </td>
                        </tr>
                        <!-- Add more rows as needed -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Upcoming Interviews -->
    <div class="card mb-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Upcoming Interviews</h5>
            <a href="<?= base_url('interviews') ?>" class="btn btn-link text-info text-decoration-none">View All</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Candidate</th>
                            <th>Position</th>
                            <th>Date & Time</th>
                            <th>Type</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="px-4">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                    <span class="fw-medium">Jane Smith</span>
                                </div>
                            </td>
                            <td>UI/UX Designer</td>
                            <td>Aug 18, 2024 10:00 AM</td>
                            <td>
                                <span class="badge bg-info bg-opacity-10 text-info">Virtual</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-success">Join</button>
                            </td>
                        </tr>
                        <!-- Add more rows as needed -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard scripts initialized');

    // Find and report any JavaScript syntax errors
    try {
        // Check for common syntax issues
        const scripts = document.querySelectorAll('script');
        scripts.forEach((script, index) => {
            if (script.innerHTML.trim()) {
                try {
                    // Try to evaluate the script content to check for syntax errors
                    new Function(script.innerHTML);
                } catch (e) {
                    console.error(`Syntax error in script #${index}:`, e.message);
                    console.log('Script content:', script.innerHTML.substring(0, 100) + '...');
                }
            }
        });
    } catch (e) {
        console.error('Error in error detection script:', e);
    }
});
</script>
<?= $this->endSection() ?>