<?php

namespace App\Controllers;

use CodeIgniter\Controller;

/**
 * SettingsController
 * 
 * Main controller for settings dashboard and navigation
 */
class SettingsController extends Controller
{
    protected $session;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->session = \Config\Services::session();
    }

    /**
     * [GET] Main settings dashboard
     * URI: /settings
     */
    public function index()
    {
        // Mock organization data for display
        $organization = [
            'id' => 1,
            'orgcode' => 'DIT',
            'name' => 'Department of Information Technology',
            'description' => 'Government IT Department',
            'orglogo' => 'public/uploads/org_logo/dit_logo.png',
            'license_status' => 'active',
            'is_active' => 1
        ];

        // Mock system settings
        $systemSettings = [
            'application_deadline_notifications' => true,
            'email_notifications' => true,
            'sms_notifications' => false,
            'auto_backup' => true,
            'backup_frequency' => 'daily',
            'max_file_upload_size' => '10MB',
            'session_timeout' => '30 minutes',
            'password_expiry_days' => 90,
            'failed_login_attempts' => 5,
            'account_lockout_duration' => '15 minutes'
        ];

        // Mock user preferences
        $userPreferences = [
            'theme' => 'light',
            'language' => 'english',
            'timezone' => 'Pacific/Port_Moresby',
            'date_format' => 'DD/MM/YYYY',
            'time_format' => '24-hour',
            'items_per_page' => 25,
            'email_digest' => 'daily',
            'dashboard_widgets' => [
                'recent_applications' => true,
                'upcoming_interviews' => true,
                'system_statistics' => true,
                'quick_actions' => true
            ]
        ];

        // Mock security settings
        $securitySettings = [
            'two_factor_auth' => false,
            'password_complexity' => 'medium',
            'login_history_retention' => '90 days',
            'audit_log_retention' => '1 year',
            'ip_whitelist_enabled' => false,
            'session_security' => 'standard'
        ];

        $data = [
            'title' => 'Settings',
            'menu' => 'settings',
            'organization' => $organization,
            'system_settings' => $systemSettings,
            'user_preferences' => $userPreferences,
            'security_settings' => $securitySettings
        ];

        return view('admin/admin_settings', $data);
    }
}
