<?php
/**
 * Interview Management - Exercises List
 * Shows exercises with selection status for interview management
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Management</h4>
                    <p class="text-muted mb-0">Manage interview sessions, schedules, and scoring for exercises in selection phase</p>
                </div>
                <div>
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercises Card -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-list text-primary me-2"></i>
                        Exercises in Selection Phase
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-info text-dark">
                        <?= count($exercises) ?> Exercise(s) Available
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($exercises)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-clipboard-list text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Exercises in Selection Phase</h5>
                    <p class="text-muted">There are currently no exercises in the selection phase available for interview management.</p>
                    <a href="<?= base_url('exercises') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Manage Exercises
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Exercise Details</th>
                                <th>Advertisement Info</th>
                                <th>Duration</th>
                                <th>Status</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($exercises as $exercise): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?= esc($exercise['exercise_name']) ?></h6>
                                            <small class="text-muted">
                                                <i class="fas fa-file-alt me-1"></i>
                                                Gazette: <?= esc($exercise['gazzetted_no']) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= esc($exercise['advertisement_no']) ?></div>
                                            <small class="text-muted">Advertisement No.</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium">
                                                <?= date('M d, Y', strtotime($exercise['publish_date_from'])) ?>
                                            </div>
                                            <small class="text-muted">to</small>
                                            <div class="fw-medium">
                                                <?= date('M d, Y', strtotime($exercise['publish_date_to'])) ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-dark">
                                            <i class="fas fa-users me-1"></i>
                                            <?= ucfirst($exercise['status']) ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>" 
                                               class="btn btn-primary btn-sm" 
                                               title="Manage Interview Sessions">
                                                <i class="fas fa-calendar-alt me-1"></i>
                                                Interview Sessions
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-outline-info btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#exerciseDetailsModal"
                                                    data-exercise='<?= json_encode($exercise) ?>'
                                                    title="View Exercise Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Exercise Details Modal -->
<div class="modal fade" id="exerciseDetailsModal" tabindex="-1" aria-labelledby="exerciseDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="exerciseDetailsModalLabel">
                    <i class="fas fa-clipboard-list me-2"></i>Exercise Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Exercise Name</label>
                        <p class="form-control-plaintext" id="modal-exercise-name">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Status</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-info text-dark" id="modal-exercise-status">-</span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Gazette Number</label>
                        <p class="form-control-plaintext" id="modal-gazzetted-no">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Advertisement Number</label>
                        <p class="form-control-plaintext" id="modal-advertisement-no">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Publish From</label>
                        <p class="form-control-plaintext" id="modal-publish-from">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Publish To</label>
                        <p class="form-control-plaintext" id="modal-publish-to">-</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">Description</label>
                        <p class="form-control-plaintext" id="modal-description">-</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="#" id="modal-manage-sessions" class="btn btn-primary">
                    <i class="fas fa-calendar-alt me-2"></i>Manage Interview Sessions
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle exercise details modal
    const exerciseDetailsModal = document.getElementById('exerciseDetailsModal');
    if (exerciseDetailsModal) {
        exerciseDetailsModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const exercise = JSON.parse(button.getAttribute('data-exercise'));
            
            // Populate modal fields
            document.getElementById('modal-exercise-name').textContent = exercise.exercise_name;
            document.getElementById('modal-exercise-status').textContent = exercise.status.charAt(0).toUpperCase() + exercise.status.slice(1);
            document.getElementById('modal-gazzetted-no').textContent = exercise.gazzetted_no;
            document.getElementById('modal-advertisement-no').textContent = exercise.advertisement_no;
            document.getElementById('modal-publish-from').textContent = new Date(exercise.publish_date_from).toLocaleDateString();
            document.getElementById('modal-publish-to').textContent = new Date(exercise.publish_date_to).toLocaleDateString();
            document.getElementById('modal-description').textContent = exercise.description || 'No description available';
            
            // Update manage sessions link
            document.getElementById('modal-manage-sessions').href = `<?= base_url('interviews/sessions/') ?>${exercise.id}`;
        });
    }
});
</script>
<?= $this->endSection() ?>
