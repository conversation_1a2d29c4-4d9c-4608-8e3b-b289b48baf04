<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Exercise Details</h5>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('exercises') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Exercise Management
                    </a>
                    <a href="<?= base_url('exercises/edit/' . $exercise['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Exercise
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Exercise Name</label>
                        <p class="form-control-plaintext"><?= esc($exercise['exercise_name']) ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Status</label>
                        <p class="form-control-plaintext">
                            <?php
                            $statusClasses = [
                                'draft' => 'bg-secondary',
                                'published' => 'bg-success',
                                'selection' => 'bg-info',
                                'archived' => 'bg-danger'
                            ];
                            $displayStatus = ucfirst($exercise['status']);
                            ?>
                            <span class="badge <?= $statusClasses[$exercise['status']] ?? 'bg-secondary' ?> text-white"><?= $displayStatus ?></span>
                        </p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Gazzetted No</label>
                        <p class="form-control-plaintext"><?= esc($exercise['gazzetted_no']) ?: 'Not specified' ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Gazzetted Date</label>
                        <p class="form-control-plaintext"><?= $exercise['gazzetted_date'] ? date('F j, Y', strtotime($exercise['gazzetted_date'])) : 'Not specified' ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Advertisement No</label>
                        <p class="form-control-plaintext"><?= esc($exercise['advertisement_no']) ?: 'Not specified' ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Advertisement Date</label>
                        <p class="form-control-plaintext"><?= $exercise['advertisement_date'] ? date('F j, Y', strtotime($exercise['advertisement_date'])) : 'Not specified' ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Mode of Advertisement</label>
                        <p class="form-control-plaintext"><?= esc($exercise['mode_of_advertisement']) ?: 'Not specified' ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Created Date</label>
                        <p class="form-control-plaintext"><?= $exercise['created_at'] ? date('F j, Y g:i A', strtotime($exercise['created_at'])) : 'Not available' ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Publish Date From</label>
                        <p class="form-control-plaintext"><?= $exercise['publish_date_from'] ? date('F j, Y', strtotime($exercise['publish_date_from'])) : 'Not specified' ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Publish Date To</label>
                        <p class="form-control-plaintext"><?= $exercise['publish_date_to'] ? date('F j, Y', strtotime($exercise['publish_date_to'])) : 'Not specified' ?></p>
                    </div>
                </div>
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Description</label>
                        <div class="border rounded p-3 bg-light">
                            <?= $exercise['description'] ? nl2br(esc($exercise['description'])) : '<em class="text-muted">No description provided</em>' ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex gap-2">
                        <a href="<?= base_url('exercises/edit/' . $exercise['id']) ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Exercise
                        </a>
                        <a href="<?= base_url('exercises/pre_screen_criteria/' . $exercise['id']) ?>" class="btn btn-warning">
                            <i class="fas fa-filter me-2"></i>Pre-Screening Criteria
                        </a>
                        <?php if ($exercise['status'] === 'draft'): ?>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="fas fa-trash me-2"></i>Delete Exercise
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    if (confirm('Are you sure you want to delete this exercise? This action cannot be undone.')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('exercises/delete/' . $exercise['id']) ?>';

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?= $this->endSection() ?>
