<?php
/**
 * View file for Application Scoring Report
 *
 * @var array $position Position details
 * @var array $scoring_data List of scoring data
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Scoring Report - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Application Scoring Report</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?> - <?= esc($position['position_reference']) ?></p>
                </div>
                <div>
                    <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>
                        Export Excel
                    </button>
                    <a href="<?= base_url('reports/positions/' . $position['exercise_id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Group:</dt>
                                <dd class="col-sm-8"><?= esc($position['group_name']) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scoring Data -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star me-2"></i>
                        Application Scoring Results (<?= count($scoring_data) ?> Applications)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($scoring_data)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No scoring data found</h5>
                            <p class="text-muted">No application scoring data is available for this position.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($scoring_data as $index => $score): ?>
                            <div class="card mb-4 border-secondary">
                                <div class="card-header bg-light">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-0">
                                                <strong><?= esc($score['first_name'] . ' ' . $score['last_name']) ?></strong>
                                                <span class="text-muted ms-2">(<?= esc($score['application_number']) ?>)</span>
                                            </h6>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h5 class="mb-0 text-primary"><?= $score['overall_percentage'] ?>%</h5>
                                                <small class="text-muted">Overall Score</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <span class="badge bg-<?= $score['rating'] === 'Excellent' ? 'success' : ($score['rating'] === 'Good' ? 'primary' : 'warning') ?> fs-6">
                                                <?= esc($score['rating']) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Applicant Information -->
                                        <div class="col-md-4">
                                            <h6 class="text-primary mb-3">Applicant Information</h6>
                                            <?php $contact = json_decode($score['contact_details'], true); ?>
                                            <dl class="row">
                                                <dt class="col-sm-5">Gender:</dt>
                                                <dd class="col-sm-7"><?= esc($score['gender']) ?></dd>
                                                <dt class="col-sm-5">Phone:</dt>
                                                <dd class="col-sm-7"><?= esc($contact['phone'] ?? 'N/A') ?></dd>
                                                <dt class="col-sm-5">Email:</dt>
                                                <dd class="col-sm-7"><?= esc($contact['email'] ?? 'N/A') ?></dd>
                                                <dt class="col-sm-5">Rated By:</dt>
                                                <dd class="col-sm-7"><?= esc($score['rated_by']) ?></dd>
                                                <dt class="col-sm-5">Rated At:</dt>
                                                <dd class="col-sm-7"><?= date('M d, Y H:i', strtotime($score['rated_at'])) ?></dd>
                                            </dl>
                                        </div>

                                        <!-- Scoring Details -->
                                        <div class="col-md-8">
                                            <h6 class="text-success mb-3">Detailed Scoring Breakdown</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>Criteria</th>
                                                            <th>Max Score</th>
                                                            <th>Score Achieved</th>
                                                            <th>Percentage</th>
                                                            <th>Progress</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($score['scoring_details'] as $detail): ?>
                                                            <tr>
                                                                <td><?= esc($detail['criteria']) ?></td>
                                                                <td class="text-center"><?= $detail['max_score'] ?></td>
                                                                <td class="text-center"><strong><?= $detail['score'] ?></strong></td>
                                                                <td class="text-center"><?= number_format($detail['percentage'], 1) ?>%</td>
                                                                <td>
                                                                    <div class="progress" style="height: 20px;">
                                                                        <div class="progress-bar bg-<?= $detail['percentage'] >= 80 ? 'success' : ($detail['percentage'] >= 60 ? 'warning' : 'danger') ?>" 
                                                                             role="progressbar" 
                                                                             style="width: <?= $detail['percentage'] ?>%"
                                                                             aria-valuenow="<?= $detail['percentage'] ?>" 
                                                                             aria-valuemin="0" 
                                                                             aria-valuemax="100">
                                                                            <?= number_format($detail['percentage'], 1) ?>%
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                    <tfoot class="table-dark">
                                                        <tr>
                                                            <th>Total</th>
                                                            <th class="text-center"><?= $score['max_total_score'] ?></th>
                                                            <th class="text-center"><strong><?= $score['total_score'] ?></strong></th>
                                                            <th class="text-center"><strong><?= $score['overall_percentage'] ?>%</strong></th>
                                                            <th>
                                                                <div class="progress" style="height: 20px;">
                                                                    <div class="progress-bar bg-primary" 
                                                                         role="progressbar" 
                                                                         style="width: <?= $score['overall_percentage'] ?>%"
                                                                         aria-valuenow="<?= $score['overall_percentage'] ?>" 
                                                                         aria-valuemin="0" 
                                                                         aria-valuemax="100">
                                                                        <?= $score['overall_percentage'] ?>%
                                                                    </div>
                                                                </div>
                                                            </th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (!empty($score['remarks'])): ?>
                                        <hr>
                                        <div class="row">
                                            <div class="col-12">
                                                <h6 class="text-info mb-2">Remarks</h6>
                                                <div class="alert alert-info">
                                                    <i class="fas fa-comment me-2"></i>
                                                    <?= esc($score['remarks']) ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Scoring Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($scoring_data) ?></h4>
                                <p class="mb-0">Total Scored</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">
                                    <?= count(array_filter($scoring_data, function($score) { return $score['rating'] === 'Excellent'; })) ?>
                                </h4>
                                <p class="mb-0">Excellent Rating</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">
                                    <?= count(array_filter($scoring_data, function($score) { return $score['rating'] === 'Good'; })) ?>
                                </h4>
                                <p class="mb-0">Good Rating</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <?php 
                                $avgScore = count($scoring_data) > 0 ? 
                                    round(array_sum(array_column($scoring_data, 'overall_percentage')) / count($scoring_data), 1) : 0;
                                ?>
                                <h4 class="text-info"><?= $avgScore ?>%</h4>
                                <p class="mb-0">Average Score</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // Mock export functionality for UI development
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show success message
        alert('Application scoring report exported successfully!\n\nFile: application_scoring_' + new Date().toISOString().slice(0,10) + '.xlsx');
    }, 2000);
}
</script>
<?= $this->endSection() ?>
