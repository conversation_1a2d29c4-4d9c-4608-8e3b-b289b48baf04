<?php
/**
 * Interview Questions Management
 * Manage interview questions for a specific position
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/1') ?>">Sessions</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/session/1/positions') ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Questions - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Questions</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-briefcase me-1"></i>
                        <?= esc($position['designation']) ?> (<?= esc($position['position_reference']) ?>)
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/session/1/positions') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Positions
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                        <i class="fas fa-plus me-2"></i>Add Question
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Info Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-briefcase text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Position</h6>
                        <small class="text-muted"><?= esc($position['designation']) ?></small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-map-marker-alt text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Location</h6>
                        <small class="text-muted"><?= esc($position['location']) ?></small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-question-circle text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Questions</h6>
                        <span class="fw-bold"><?= count($questions) ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-clock text-info mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Total Duration</h6>
                        <span class="fw-bold"><?= array_sum(array_column($questions, 'expected_duration')) ?> min</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Questions Management -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle text-primary me-2"></i>
                        Interview Questions
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm active" data-filter="all">
                            All
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="Technical">
                            Technical
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="Behavioral">
                            Behavioral
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="Situational">
                            Situational
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($questions)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-question-circle text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Questions Added</h5>
                    <p class="text-muted">Add interview questions to structure the interview process for this position.</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                        <i class="fas fa-plus me-2"></i>Add First Question
                    </button>
                </div>
            <?php else: ?>
                <div class="row" id="questionsContainer">
                    <?php foreach ($questions as $index => $question): ?>
                        <div class="col-12 mb-3 question-item" data-category="<?= $question['category'] ?>">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-light border-0">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-primary me-2">Q<?= $index + 1 ?></span>
                                                <span class="badge bg-info text-dark me-2"><?= esc($question['category']) ?></span>
                                                <span class="badge bg-warning text-dark me-2">
                                                    <i class="fas fa-weight-hanging me-1"></i><?= $question['weight'] ?>%
                                                </span>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-clock me-1"></i><?= $question['expected_duration'] ?> min
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <div class="btn-group" role="group">
                                                <button type="button" 
                                                        class="btn btn-outline-primary btn-sm edit-question-btn" 
                                                        data-question='<?= json_encode($question) ?>'
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#editQuestionModal">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" 
                                                        class="btn btn-outline-danger btn-sm delete-question-btn" 
                                                        data-question-id="<?= $question['id'] ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0"><?= esc($question['question']) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Questions Summary -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <strong>Total Questions:</strong> <?= count($questions) ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Total Weight:</strong> <?= array_sum(array_column($questions, 'weight')) ?>%
                                </div>
                                <div class="col-md-3">
                                    <strong>Total Duration:</strong> <?= array_sum(array_column($questions, 'expected_duration')) ?> minutes
                                </div>
                                <div class="col-md-3">
                                    <strong>Categories:</strong> <?= count(array_unique(array_column($questions, 'category'))) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Question Modal -->
<div class="modal fade" id="addQuestionModal" tabindex="-1" aria-labelledby="addQuestionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addQuestionModalLabel">
                    <i class="fas fa-plus me-2"></i>Add Interview Question
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= form_open('interviews/position/' . $position['id'] . '/questions/save', ['id' => 'addQuestionForm']) ?>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Technical">Technical</option>
                                <option value="Behavioral">Behavioral</option>
                                <option value="Situational">Situational</option>
                                <option value="General">General</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="weight" class="form-label">Weight (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="weight" name="weight" min="1" max="100" required>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="expected_duration" class="form-label">Duration (min) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="expected_duration" name="expected_duration" min="1" max="60" required>
                        </div>
                        
                        <div class="col-12">
                            <label for="question" class="form-label">Question <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="question" name="question" rows="4" placeholder="Enter the interview question..." required></textarea>
                        </div>
                    </div>
                <?= form_close() ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="addQuestionForm" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Add Question
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Question Modal -->
<div class="modal fade" id="editQuestionModal" tabindex="-1" aria-labelledby="editQuestionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editQuestionModalLabel">
                    <i class="fas fa-edit me-2"></i>Edit Interview Question
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= form_open('interviews/position/' . $position['id'] . '/questions/save', ['id' => 'editQuestionForm']) ?>
                    <input type="hidden" id="edit_question_id" name="question_id">
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit_category" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Technical">Technical</option>
                                <option value="Behavioral">Behavioral</option>
                                <option value="Situational">Situational</option>
                                <option value="General">General</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="edit_weight" class="form-label">Weight (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit_weight" name="weight" min="1" max="100" required>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="edit_expected_duration" class="form-label">Duration (min) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit_expected_duration" name="expected_duration" min="1" max="60" required>
                        </div>
                        
                        <div class="col-12">
                            <label for="edit_question" class="form-label">Question <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="edit_question" name="question" rows="4" required></textarea>
                        </div>
                    </div>
                <?= form_close() ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editQuestionForm" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Update Question
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter question items
            const items = document.querySelectorAll('.question-item');
            items.forEach(item => {
                if (filter === 'all' || item.getAttribute('data-category') === filter) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Add question form
    const addQuestionForm = document.getElementById('addQuestionForm');
    if (addQuestionForm) {
        addQuestionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addQuestionModal'));
                    modal.hide();
                    window.location.reload();
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toastr !== 'undefined') {
                    toastr.error('An error occurred while adding the question');
                } else {
                    alert('An error occurred while adding the question');
                }
            });
        });
    }

    // Edit question modal
    const editQuestionModal = document.getElementById('editQuestionModal');
    if (editQuestionModal) {
        editQuestionModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const question = JSON.parse(button.getAttribute('data-question'));
            
            document.getElementById('edit_question_id').value = question.id;
            document.getElementById('edit_category').value = question.category;
            document.getElementById('edit_weight').value = question.weight;
            document.getElementById('edit_expected_duration').value = question.expected_duration;
            document.getElementById('edit_question').value = question.question;
        });
    }

    // Edit question form
    const editQuestionForm = document.getElementById('editQuestionForm');
    if (editQuestionForm) {
        editQuestionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    const modal = bootstrap.Modal.getInstance(editQuestionModal);
                    modal.hide();
                    window.location.reload();
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toastr !== 'undefined') {
                    toastr.error('An error occurred while updating the question');
                } else {
                    alert('An error occurred while updating the question');
                }
            });
        });
    }

    // Delete question
    document.querySelectorAll('.delete-question-btn').forEach(button => {
        button.addEventListener('click', function() {
            const questionId = this.getAttribute('data-question-id');
            
            if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
                // Mock delete action
                if (typeof toastr !== 'undefined') {
                    toastr.success('Question deleted successfully');
                } else {
                    alert('Question deleted successfully');
                }
                window.location.reload();
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
