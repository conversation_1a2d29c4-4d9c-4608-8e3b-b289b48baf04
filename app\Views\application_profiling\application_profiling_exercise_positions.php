<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('profile_applications_exercise') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-briefcase me-2"></i>Positions for Profiling</h2>
            <p class="text-muted">
                Exercise: <strong><?= esc($exercise['exercise_name']) ?></strong><br>
                <small>Advertisement No: <?= esc($exercise['advertisement_no']) ?> | Gazetted No: <?= esc($exercise['gazzetted_no']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('profile_applications_exercise') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <!-- Exercise Information Card -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Exercise Name:</strong><br>
                            <?= esc($exercise['exercise_name']) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Advertisement No:</strong><br>
                            <?= esc($exercise['advertisement_no']) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Published Period:</strong><br>
                            <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> - <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Status:</strong><br>
                            <span class="badge badge-success"><?= ucfirst(esc($exercise['status'])) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="card">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">
                <i class="fas fa-briefcase me-2"></i>Available Positions
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No positions found for this exercise.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="12%">Reference</th>
                                <th width="20%">Position Title</th>
                                <th width="12%">Group</th>
                                <th width="10%">Classification</th>
                                <th width="15%">Location</th>
                                <th width="8%">Apps</th>
                                <th width="8%">Pending</th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td>
                                        <code><?= esc($position['position_reference']) ?></code>
                                    </td>
                                    <td>
                                        <strong><?= esc($position['designation']) ?></strong><br>
                                        <small class="text-muted"><?= esc($position['award']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= esc($position['position_group_name']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= esc($position['classification']) ?></span>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?= esc($position['location']) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= $position['applications_count'] ?></span>
                                    </td>
                                    <td>
                                        <?php if ($position['pending_prescreen_count'] > 0): ?>
                                            <span class="badge bg-warning"><?= $position['pending_prescreen_count'] ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('profile_applications_exercise/position/' . $position['id'] . '/profile') ?>"
                                           class="btn btn-sm btn-primary"
                                           data-bs-toggle="tooltip"
                                           title="View Position Profile">
                                            <i class="fas fa-id-badge"></i> Profile
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by row number
        language: {
            search: "Search positions:",
            lengthMenu: "Show _MENU_ positions per page",
            info: "Showing _START_ to _END_ of _TOTAL_ positions",
            emptyTable: "No positions available in this exercise",
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    console.log('Exercise Positions view loaded');
});
</script>
<?= $this->endSection() ?>
