<?php
/**
 * Edit Interview Session
 * Form to edit an existing interview session
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Edit Session</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Edit Interview Session</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-clipboard-list me-1"></i>
                        <?= esc($exercise['exercise_name']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Sessions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Session Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Edit Session: <?= esc($session['session_name']) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('interviews/sessions/update/' . $session['id'], ['id' => 'editSessionForm']) ?>
                        <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                        
                        <div class="row g-3">
                            <!-- Session Name -->
                            <div class="col-md-6">
                                <label for="session_name" class="form-label">
                                    Session Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="session_name" 
                                       name="session_name" 
                                       value="<?= esc($session['session_name']) ?>"
                                       placeholder="e.g., Technical Interview Session"
                                       required>
                                <div class="form-text">Enter a descriptive name for this interview session</div>
                            </div>

                            <!-- Session Status -->
                            <div class="col-md-6">
                                <label for="status" class="form-label">
                                    Status <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="draft" <?= $session['status'] === 'draft' ? 'selected' : '' ?>>Draft</option>
                                    <option value="active" <?= $session['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="completed" <?= $session['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                                    <option value="cancelled" <?= $session['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                </select>
                                <div class="form-text">Update the status of this session</div>
                            </div>

                            <!-- Session Description -->
                            <div class="col-12">
                                <label for="session_description" class="form-label">
                                    Session Description
                                </label>
                                <textarea class="form-control" 
                                          id="session_description" 
                                          name="session_description" 
                                          rows="3"
                                          placeholder="Describe the purpose and scope of this interview session"><?= esc($session['session_description']) ?></textarea>
                                <div class="form-text">Provide additional details about this interview session</div>
                            </div>

                            <!-- Start Date -->
                            <div class="col-md-6">
                                <label for="start_date" class="form-label">
                                    Start Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="start_date" 
                                       name="start_date" 
                                       value="<?= $session['start_date'] ?>"
                                       required>
                                <div class="form-text">When will this interview session begin?</div>
                            </div>

                            <!-- End Date -->
                            <div class="col-md-6">
                                <label for="end_date" class="form-label">
                                    End Date <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="end_date" 
                                       name="end_date" 
                                       value="<?= $session['end_date'] ?>"
                                       required>
                                <div class="form-text">When will this interview session end?</div>
                            </div>

                            <!-- Interview Type -->
                            <div class="col-md-6">
                                <label for="interview_type" class="form-label">
                                    Interview Type
                                </label>
                                <select class="form-select" id="interview_type" name="interview_type">
                                    <option value="">Select Type</option>
                                    <option value="panel" selected>Panel Interview</option>
                                    <option value="individual">Individual Interview</option>
                                    <option value="group">Group Interview</option>
                                    <option value="technical">Technical Assessment</option>
                                    <option value="behavioral">Behavioral Interview</option>
                                </select>
                                <div class="form-text">Choose the type of interview format</div>
                            </div>

                            <!-- Duration -->
                            <div class="col-md-6">
                                <label for="default_duration" class="form-label">
                                    Default Duration (minutes)
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="default_duration" 
                                       name="default_duration" 
                                       value="60"
                                       min="15" 
                                       max="240"
                                       step="15">
                                <div class="form-text">Default interview duration for this session</div>
                            </div>

                            <!-- Venue -->
                            <div class="col-12">
                                <label for="venue" class="form-label">
                                    Interview Venue
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="venue" 
                                       name="venue" 
                                       placeholder="e.g., Conference Room A, Government Building">
                                <div class="form-text">Where will the interviews take place?</div>
                            </div>

                            <!-- Special Instructions -->
                            <div class="col-12">
                                <label for="special_instructions" class="form-label">
                                    Special Instructions
                                </label>
                                <textarea class="form-control" 
                                          id="special_instructions" 
                                          name="special_instructions" 
                                          rows="3"
                                          placeholder="Any special instructions for candidates or interviewers"></textarea>
                                <div class="form-text">Additional instructions or requirements</div>
                            </div>
                        </div>

                        <!-- Session Information -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>Session Information
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Created:</strong> <?= date('M d, Y H:i', strtotime($session['created_at'])) ?>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Current Status:</strong> 
                                            <span class="badge <?= $session['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?>">
                                                <?= ucfirst($session['status']) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>" 
                                       class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="updateBtn">
                                        <i class="fas fa-save me-2"></i>Update Session
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editSessionForm');
    const updateBtn = document.getElementById('updateBtn');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');

    // Set minimum date to today for future dates only
    const today = new Date().toISOString().split('T')[0];
    
    // Update end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInput.min = this.value;
        if (endDateInput.value && endDateInput.value < this.value) {
            endDateInput.value = this.value;
        }
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Disable submit button to prevent double submission
        updateBtn.disabled = true;
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

        // Validate dates
        if (startDateInput.value && endDateInput.value) {
            if (new Date(endDateInput.value) < new Date(startDateInput.value)) {
                alert('End date cannot be earlier than start date');
                updateBtn.disabled = false;
                updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Update Session';
                return;
            }
        }

        // Submit form data
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                if (typeof toastr !== 'undefined') {
                    toastr.success(data.message);
                } else {
                    alert(data.message);
                }
                
                // Redirect to sessions list
                setTimeout(() => {
                    window.location.href = '<?= base_url('interviews/sessions/' . $exercise['id']) ?>';
                }, 1000);
            } else {
                // Show error message
                if (typeof toastr !== 'undefined') {
                    toastr.error(data.message || 'Failed to update session');
                } else {
                    alert('Error: ' + (data.message || 'Failed to update session'));
                }
                
                // Re-enable submit button
                updateBtn.disabled = false;
                updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Update Session';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('An error occurred while updating the session');
            } else {
                alert('An error occurred while updating the session');
            }
            
            // Re-enable submit button
            updateBtn.disabled = false;
            updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Update Session';
        });
    });

    // Initialize date validation
    if (startDateInput.value) {
        endDateInput.min = startDateInput.value;
    }
});
</script>
<?= $this->endSection() ?>
