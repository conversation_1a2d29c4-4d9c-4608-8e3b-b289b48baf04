<?php

namespace App\Controllers;

// Remove all model imports since we're using mock data

class ApplicationPreScreeningController extends BaseController
{
    // Remove model properties - replaced with mock data
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']); // Assuming application_helper exists
        $this->session = \Config\Services::session();
        // Remove all model instantiations - using mock data instead
    }

    /**
     * Generate mock application data
     */
    private function getMockApplications()
    {
        return [
            [
                'id' => 1,
                'applicant_id' => 101,
                'position_id' => 201,
                'application_number' => 'APP-2024-001',
                'recieved_acknowledged' => '2024-01-15 10:30:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-10 08:00:00',
                'fname' => 'John',
                'lname' => 'Doe',
                'position_name' => 'Senior Software Engineer'
            ],
            [
                'id' => 2,
                'applicant_id' => 102,
                'position_id' => 202,
                'application_number' => 'APP-2024-002',
                'recieved_acknowledged' => '2024-01-16 14:20:00',
                'pre_screened' => '2024-01-17 16:00:00',
                'pre_screened_status' => 'passed',
                'pre_screened_remarks' => 'Meets all requirements',
                'created_at' => '2024-01-11 09:15:00',
                'fname' => 'Jane',
                'lname' => 'Smith',
                'position_name' => 'Project Manager'
            ],
            [
                'id' => 3,
                'applicant_id' => 103,
                'position_id' => 203,
                'application_number' => 'APP-2024-003',
                'recieved_acknowledged' => '2024-01-17 11:45:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-12 14:30:00',
                'fname' => 'Michael',
                'lname' => 'Johnson',
                'position_name' => 'Data Analyst'
            ],
            [
                'id' => 4,
                'applicant_id' => 104,
                'position_id' => 204,
                'application_number' => 'APP-2024-004',
                'recieved_acknowledged' => '2024-01-18 09:30:00',
                'pre_screened' => '2024-01-19 10:15:00',
                'pre_screened_status' => 'failed',
                'pre_screened_remarks' => 'Does not meet minimum education requirements',
                'created_at' => '2024-01-13 16:45:00',
                'fname' => 'Sarah',
                'lname' => 'Williams',
                'position_name' => 'Research Officer'
            ]
        ];
    }

    /**
     * Generate mock applicant data
     */
    private function getMockApplicant($applicantId)
    {
        $applicants = [
            101 => [
                'id' => 101,
                'unique_id' => 'APL-101',
                'email' => '<EMAIL>',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'dobirth' => '1990-05-15',
                'place_of_origin' => 'New York',
                'contact_details' => '{"phone": "+1234567890", "address": "123 Main St"}',
                'citizenship' => 'American',
                'marital_status' => 'Single',
                'current_employer' => 'Tech Corp',
                'current_position' => 'Software Developer',
                'current_salary' => '$75,000'
            ],
            102 => [
                'id' => 102,
                'unique_id' => 'APL-102',
                'email' => '<EMAIL>',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'dobirth' => '1988-09-22',
                'place_of_origin' => 'California',
                'contact_details' => '{"phone": "+1234567891", "address": "456 Oak Ave"}',
                'citizenship' => 'American',
                'marital_status' => 'Married',
                'current_employer' => 'Innovation Inc',
                'current_position' => 'Team Lead',
                'current_salary' => '$85,000'
            ],
            103 => [
                'id' => 103,
                'unique_id' => 'APL-103',
                'email' => '<EMAIL>',
                'first_name' => 'Michael',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'dobirth' => '1992-03-10',
                'place_of_origin' => 'Texas',
                'contact_details' => '{"phone": "+1234567892", "address": "789 Pine St"}',
                'citizenship' => 'American',
                'marital_status' => 'Single',
                'current_employer' => 'Data Solutions',
                'current_position' => 'Data Analyst',
                'current_salary' => '$68,000'
            ],
            104 => [
                'id' => 104,
                'unique_id' => 'APL-104',
                'email' => '<EMAIL>',
                'first_name' => 'Sarah',
                'last_name' => 'Williams',
                'gender' => 'Female',
                'dobirth' => '1985-11-08',
                'place_of_origin' => 'Florida',
                'contact_details' => '{"phone": "+1234567893", "address": "321 Elm Dr"}',
                'citizenship' => 'American',
                'marital_status' => 'Married',
                'current_employer' => 'Research Labs',
                'current_position' => 'Research Assistant',
                'current_salary' => '$58,000'
            ]
        ];

        return $applicants[$applicantId] ?? [];
    }

    /**
     * Generate mock application with detailed information
     */
    private function getMockApplicationDetails($id)
    {
        $applications = [
            1 => [
                'id' => 1,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 101,
                'position_id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'date_of_birth' => '1990-05-15',
                'place_of_origin' => 'Port Moresby, NCD',
                'id_photo_path' => '/uploads/photos/john_doe_photo.jpg',
                'contact_details' => '+675 7123 4567',
                'location_address' => '123 Main Street, Section 4, Port Moresby, NCD',
                'id_numbers' => json_encode([
                    'national_id' => 'PNG123456789',
                    'passport' => '********',
                    'drivers_license' => 'DL987654321'
                ]),
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Tech Solutions PNG',
                'current_position' => 'Software Developer',
                'current_salary' => 'K55,000 per annum',
                'marital_status' => 'Single',
                'date_of_marriage' => null,
                'spouse_employer' => null,
                'children' => json_encode([]),
                'offence_convicted' => 'No',
                'referees' => json_encode([
                    [
                        'name' => 'Dr. Michael Smith',
                        'position' => 'Senior Software Architect',
                        'organization' => 'Tech Solutions PNG',
                        'phone' => '+675 7234 5678',
                        'email' => '<EMAIL>',
                        'relationship' => 'Direct Supervisor'
                    ],
                    [
                        'name' => 'Prof. Sarah Johnson',
                        'position' => 'Head of Computer Science',
                        'organization' => 'University of Papua New Guinea',
                        'phone' => '+675 7345 6789',
                        'email' => '<EMAIL>',
                        'relationship' => 'Former Lecturer'
                    ]
                ]),
                'how_did_you_hear_about_us' => 'Government website and newspaper advertisement',
                'signature_path' => '/uploads/signatures/john_doe_signature.png',
                'publications' => json_encode([
                    'Research Paper on AI Applications in PNG - Journal of Pacific Technology, 2023',
                    'Mobile App Development Best Practices - PNG IT Conference, 2022'
                ]),
                'awards' => json_encode([
                    'Best Graduate Student - University of Papua New Guinea, 2018',
                    'Innovation Award - Tech Solutions PNG, 2022'
                ]),
                'is_received' => 1,
                'files_extracted_texts' => 'Software developer with 5+ years experience in web development, mobile applications, and database management...',
                'received_status' => 'acknowledged',
                'received_by' => 1,
                'received_at' => '2024-01-12 14:20:00',
                'application_status' => 'pending_prescreen',
                'remarks' => 'Complete application with all required documents',
                'pre_screened_status' => null,
                'pre_screened_at' => null,
                'pre_screened_by' => null,
                'pre_screened_remarks' => null,
                'pre_screened_criteria_results' => null,
                'created_at' => '2024-01-10 09:30:00',
                'updated_at' => '2024-01-12 14:20:00',
                'recieved_acknowledged' => '2024-01-12 14:20:00'
            ],
            2 => [
                'id' => 2,
                'applicant_id' => 102,
                'position_id' => 202,
                'designation' => 'Project Manager',
                'group_name' => 'Management',
                'exercise_id' => 302,
                'exercise_name' => 'Management Recruitment 2024',
                'pre_screen_criteria' => json_encode([
                    ['criterion' => 'Bachelor\'s degree in Business or related field', 'required' => true],
                    ['criterion' => 'Minimum 3 years of project management experience', 'required' => true],
                    ['criterion' => 'PMP certification preferred', 'required' => false],
                    ['criterion' => 'Leadership experience', 'required' => true]
                ]),
                'application_number' => 'APP-2024-002',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'created_at' => '2024-01-11 09:15:00',
                'recieved_acknowledged' => '2024-01-16 14:20:00',
                'pre_screened' => '2024-01-17 16:00:00',
                'pre_screened_status' => 'passed',
                'pre_screened_remarks' => 'Meets all requirements'
            ],
            3 => [
                'id' => 3,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 103,
                'position_id' => 1,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Michael',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'date_of_birth' => '1992-03-10',
                'place_of_origin' => 'Mount Hagen, Western Highlands Province',
                'id_photo_path' => '/uploads/photos/michael_johnson_photo.jpg',
                'contact_details' => '+675 7123 4569',
                'location_address' => '789 Pine Street, Boroko, Port Moresby, NCD',
                'id_numbers' => json_encode([
                    'national_id' => 'PNG987654321',
                    'passport' => '********',
                    'drivers_license' => 'DL123456789'
                ]),
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Data Solutions PNG',
                'current_position' => 'Data Analyst',
                'current_salary' => 'K48,000 per annum',
                'marital_status' => 'Married',
                'date_of_marriage' => '2020-02-14',
                'spouse_employer' => 'PNG Banking Corporation',
                'children' => json_encode([
                    ['name' => 'Emma Johnson', 'age' => 2, 'date_of_birth' => '2022-01-15']
                ]),
                'offence_convicted' => 'No',
                'referees' => json_encode([
                    [
                        'name' => 'Dr. Patricia Williams',
                        'position' => 'Senior Data Scientist',
                        'organization' => 'Data Solutions PNG',
                        'phone' => '+675 7456 7890',
                        'email' => '<EMAIL>',
                        'relationship' => 'Team Leader'
                    ],
                    [
                        'name' => 'Mr. James Brown',
                        'position' => 'Statistics Professor',
                        'organization' => 'University of Papua New Guinea',
                        'phone' => '+675 7567 8901',
                        'email' => '<EMAIL>',
                        'relationship' => 'Academic Supervisor'
                    ]
                ]),
                'how_did_you_hear_about_us' => 'Professional network and LinkedIn',
                'signature_path' => '/uploads/signatures/michael_johnson_signature.png',
                'publications' => json_encode([
                    'Statistical Analysis of PNG Economic Indicators - Pacific Economic Review, 2023',
                    'Data Visualization Techniques for Government Reporting - PNG Statistics Conference, 2022'
                ]),
                'awards' => json_encode([
                    'Outstanding Graduate - University of Papua New Guinea, 2020',
                    'Employee of the Year - Data Solutions PNG, 2023'
                ]),
                'is_received' => 1,
                'files_extracted_texts' => 'Experienced data analyst with strong background in statistical analysis, data visualization, and business intelligence...',
                'received_status' => 'acknowledged',
                'received_by' => 1,
                'received_at' => '2024-01-17 11:45:00',
                'application_status' => 'pending_prescreen',
                'remarks' => 'Strong analytical background, all documents submitted',
                'pre_screened_status' => null,
                'pre_screened_at' => null,
                'pre_screened_by' => null,
                'pre_screened_remarks' => null,
                'pre_screened_criteria_results' => null,
                'created_at' => '2024-01-12 14:30:00',
                'updated_at' => '2024-01-17 11:45:00',
                'recieved_acknowledged' => '2024-01-17 11:45:00'
            ],
            4 => [
                'id' => 4,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 104,
                'position_id' => 1,
                'application_number' => 'APP-2024-004',
                'first_name' => 'Sarah',
                'last_name' => 'Williams',
                'gender' => 'Female',
                'date_of_birth' => '1985-11-08',
                'place_of_origin' => 'Florida, USA',
                'contact_details' => '+675 7123 4570',
                'location_address' => '321 Elm Drive, Port Moresby',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Research Labs PNG',
                'current_position' => 'Research Assistant',
                'current_salary' => 'K42,000',
                'marital_status' => 'Married',
                'is_received' => 1,
                'received_status' => 'acknowledged',
                'received_at' => '2024-01-18 09:30:00',
                'application_status' => 'pre_screened',
                'pre_screened_status' => 'failed',
                'pre_screened_at' => '2024-01-19 10:15:00',
                'pre_screened_by' => 1,
                'pre_screened_remarks' => 'Does not meet minimum education requirements',
                'pre_screened_criteria_results' => null,
                'created_at' => '2024-01-13 16:45:00',
                'updated_at' => '2024-01-19 10:15:00',
                'recieved_acknowledged' => '2024-01-18 09:30:00'
            ]
        ];

        return $applications[$id] ?? null;
    }

    /**
     * Generate mock experience data
     */
    private function getMockExperiences($applicationId)
    {
        $experiences = [
            1 => [
                [
                    'id' => 1,
                    'application_id' => 1,
                    'applicant_id' => 101,
                    'employer' => 'Tech Solutions PNG',
                    'employer_contacts_address' => 'Level 3, Deloitte Tower, Port Moresby, NCD\nPhone: +************\nEmail: <EMAIL>',
                    'position' => 'Software Developer',
                    'date_from' => '2020-01-15',
                    'date_to' => '2023-12-31',
                    'achievements' => 'Led development of 3 major web applications, Reduced system downtime by 40%, Mentored 2 junior developers',
                    'work_description' => 'Developed and maintained web applications using React, Node.js, and PostgreSQL. Collaborated with cross-functional teams to deliver high-quality software solutions. Implemented automated testing procedures and CI/CD pipelines.',
                    'created_by' => 101,
                    'updated_by' => 101,
                    'created_at' => '2024-01-10 09:30:00',
                    'updated_at' => '2024-01-10 09:30:00'
                ],
                [
                    'id' => 2,
                    'application_id' => 1,
                    'applicant_id' => 101,
                    'employer' => 'Digital Innovations Ltd',
                    'employer_contacts_address' => 'Section 2, Waigani, Port Moresby, NCD\nPhone: +************\nEmail: <EMAIL>',
                    'position' => 'Junior Software Developer',
                    'date_from' => '2018-06-01',
                    'date_to' => '2019-12-31',
                    'achievements' => 'Completed 15+ feature implementations, Improved code quality through peer reviews, Gained expertise in multiple programming languages',
                    'work_description' => 'Maintained legacy systems and developed new features using Java and MySQL. Participated in agile development processes and contributed to system documentation. Assisted in database optimization and performance tuning.',
                    'created_by' => 101,
                    'updated_by' => 101,
                    'created_at' => '2024-01-10 09:30:00',
                    'updated_at' => '2024-01-10 09:30:00'
                ]
            ],
            2 => [
                [
                    'id' => 3,
                    'application_id' => 2,
                    'organization' => 'Innovation Inc',
                    'position' => 'Team Lead',
                    'start_date' => '2021-03-01',
                    'end_date' => null,
                    'duties' => 'Leading a team of 8 developers, project planning and execution',
                    'salary' => '$85,000',
                    'reason_for_leaving' => null
                ]
            ],
            3 => [
                [
                    'id' => 4,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'employer' => 'Data Solutions PNG',
                    'employer_contacts_address' => 'Level 2, Pacific Place, Port Moresby, NCD\nPhone: +************\nEmail: <EMAIL>',
                    'position' => 'Data Analyst',
                    'date_from' => '2022-01-15',
                    'date_to' => null,
                    'achievements' => 'Developed 20+ automated reports, Improved data processing efficiency by 60%, Trained 5 staff members on new analytics tools',
                    'work_description' => 'Conducted statistical analysis and generated comprehensive reports for government and private sector clients. Created interactive dashboards using Tableau and Power BI. Managed large datasets and performed data cleaning and validation procedures.',
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:30:00',
                    'updated_at' => '2024-01-12 14:30:00'
                ],
                [
                    'id' => 5,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'employer' => 'PNG National Statistics Office',
                    'employer_contacts_address' => 'Wards Road, Port Moresby, NCD\nPhone: +************\nEmail: <EMAIL>',
                    'position' => 'Statistical Assistant',
                    'date_from' => '2020-03-01',
                    'date_to' => '2021-12-31',
                    'achievements' => 'Contributed to national census data analysis, Developed standardized reporting templates, Received commendation for accuracy and attention to detail',
                    'work_description' => 'Assisted in collection and analysis of national statistical data. Prepared reports for government planning and policy development. Conducted field surveys and data validation activities.',
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:30:00',
                    'updated_at' => '2024-01-12 14:30:00'
                ]
            ],
            4 => [
                [
                    'id' => 5,
                    'application_id' => 4,
                    'organization' => 'Research Labs PNG',
                    'position' => 'Research Assistant',
                    'start_date' => '2020-06-01',
                    'end_date' => null,
                    'duties' => 'Conducting research studies, data collection, laboratory work',
                    'salary' => 'K42,000',
                    'reason_for_leaving' => null
                ]
            ]
        ];

        return $experiences[$applicationId] ?? [];
    }

    /**
     * Generate mock education data
     */
    private function getMockEducation($applicationId)
    {
        $education = [
            1 => [
                [
                    'id' => 1,
                    'application_id' => 1,
                    'applicant_id' => 101,
                    'institution' => 'University of Papua New Guinea',
                    'course' => 'Bachelor of Computer Science',
                    'date_from' => '2014-02-01',
                    'date_to' => '2018-11-30',
                    'education_level' => 'Bachelor Degree',
                    'units' => json_encode([
                        'Programming Fundamentals', 'Data Structures and Algorithms', 'Database Systems',
                        'Software Engineering', 'Computer Networks', 'Web Development',
                        'Mobile Application Development', 'Artificial Intelligence', 'Project Management'
                    ]),
                    'created_by' => 101,
                    'updated_by' => 101,
                    'created_at' => '2024-01-10 09:30:00',
                    'updated_at' => '2024-01-10 09:30:00'
                ],
                [
                    'id' => 2,
                    'application_id' => 1,
                    'applicant_id' => 101,
                    'institution' => 'PNG Institute of Technology',
                    'course' => 'Certificate IV in Information Technology',
                    'date_from' => '2019-01-15',
                    'date_to' => '2019-06-30',
                    'education_level' => 'Certificate',
                    'units' => json_encode([
                        'Advanced Web Development', 'Cloud Computing', 'Cybersecurity Fundamentals',
                        'DevOps Practices', 'API Development'
                    ]),
                    'created_by' => 101,
                    'updated_by' => 101,
                    'created_at' => '2024-01-10 09:30:00',
                    'updated_at' => '2024-01-10 09:30:00'
                ]
            ],
            2 => [
                [
                    'id' => 3,
                    'application_id' => 2,
                    'institution' => 'Business University',
                    'qualification' => 'MBA in Project Management',
                    'start_year' => '2018',
                    'end_year' => '2020',
                    'grade' => 'Distinction',
                    'specialization' => 'Project Management'
                ]
            ],
            3 => [
                [
                    'id' => 4,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'institution' => 'University of Papua New Guinea',
                    'course' => 'Bachelor of Mathematics and Statistics',
                    'date_from' => '2016-02-01',
                    'date_to' => '2020-11-30',
                    'education_level' => 'Bachelor Degree',
                    'units' => json_encode([
                        'Statistical Methods', 'Probability Theory', 'Data Analysis', 'Research Methods',
                        'Mathematical Modeling', 'Econometrics', 'Survey Design', 'Statistical Software',
                        'Business Statistics', 'Multivariate Analysis'
                    ]),
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:30:00',
                    'updated_at' => '2024-01-12 14:30:00'
                ],
                [
                    'id' => 5,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'institution' => 'PNG Institute of Public Administration',
                    'course' => 'Certificate in Data Analysis and Visualization',
                    'date_from' => '2021-03-01',
                    'date_to' => '2021-08-31',
                    'education_level' => 'Certificate',
                    'units' => json_encode([
                        'Advanced Excel', 'Tableau', 'Power BI', 'SQL Database Queries',
                        'Python for Data Analysis', 'R Programming'
                    ]),
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:30:00',
                    'updated_at' => '2024-01-12 14:30:00'
                ]
            ],
            4 => [
                [
                    'id' => 5,
                    'application_id' => 4,
                    'institution' => 'Community College',
                    'qualification' => 'Diploma in Laboratory Technology',
                    'start_year' => '2018',
                    'end_year' => '2020',
                    'grade' => 'Credit',
                    'specialization' => 'Medical Laboratory Technology'
                ]
            ]
        ];

        return $education[$applicationId] ?? [];
    }

    /**
     * Generate mock files data
     */
    private function getMockFiles($applicationId)
    {
        $files = [
            1 => [
                [
                    'id' => 1,
                    'application_id' => 1,
                    'applicant_id' => 101,
                    'file_title' => 'Curriculum Vitae',
                    'file_description' => 'Comprehensive CV detailing education, work experience, and skills',
                    'file_path' => '/uploads/applications/1/john_doe_cv.pdf',
                    'extracted_texts' => 'John Doe, Software Developer with 5+ years experience in web development, mobile applications, and database management. Proficient in React, Node.js, Python, and PostgreSQL...',
                    'created_by' => 101,
                    'updated_by' => 101,
                    'created_at' => '2024-01-10 08:15:00',
                    'updated_at' => '2024-01-10 08:15:00'
                ],
                [
                    'id' => 2,
                    'application_id' => 1,
                    'applicant_id' => 101,
                    'file_title' => 'Cover Letter',
                    'file_description' => 'Personalized cover letter for Senior Software Engineer position',
                    'file_path' => '/uploads/applications/1/john_doe_cover_letter.pdf',
                    'extracted_texts' => 'Dear Hiring Manager, I am writing to express my strong interest in the Senior Software Engineer position. With my extensive background in software development...',
                    'created_by' => 101,
                    'updated_by' => 101,
                    'created_at' => '2024-01-10 08:16:00',
                    'updated_at' => '2024-01-10 08:16:00'
                ],
                [
                    'id' => 3,
                    'application_id' => 1,
                    'applicant_id' => 101,
                    'file_title' => 'Academic Transcripts',
                    'file_description' => 'Official university transcripts and certificates',
                    'file_path' => '/uploads/applications/1/john_doe_transcripts.pdf',
                    'extracted_texts' => 'University of Papua New Guinea, Bachelor of Computer Science, First Class Honours, GPA: 3.8/4.0, Graduated November 2018...',
                    'created_by' => 101,
                    'updated_by' => 101,
                    'created_at' => '2024-01-10 08:17:00',
                    'updated_at' => '2024-01-10 08:17:00'
                ]
            ],
            2 => [
                [
                    'id' => 3,
                    'application_id' => 2,
                    'file_type' => 'resume',
                    'file_name' => 'jane_smith_cv.pdf',
                    'file_path' => '/uploads/applications/2/jane_smith_cv.pdf',
                    'file_size' => '1.8MB',
                    'uploaded_at' => '2024-01-11 09:20:00'
                ]
            ],
            3 => [
                [
                    'id' => 6,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'file_title' => 'Curriculum Vitae',
                    'file_description' => 'Detailed CV highlighting statistical analysis and data science experience',
                    'file_path' => '/uploads/applications/3/michael_johnson_cv.pdf',
                    'extracted_texts' => 'Michael Johnson, Data Analyst with 4+ years experience in statistical analysis, data visualization, and business intelligence. Expert in Tableau, Power BI, SQL, Python, and R...',
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:35:00',
                    'updated_at' => '2024-01-12 14:35:00'
                ],
                [
                    'id' => 7,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'file_title' => 'Cover Letter',
                    'file_description' => 'Application letter for Senior Software Engineer position',
                    'file_path' => '/uploads/applications/3/michael_johnson_cover_letter.pdf',
                    'extracted_texts' => 'Dear Selection Committee, I am excited to apply for the Senior Software Engineer position. My background in data analysis and statistical modeling provides a unique perspective...',
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:36:00',
                    'updated_at' => '2024-01-12 14:36:00'
                ],
                [
                    'id' => 8,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'file_title' => 'Academic Certificates',
                    'file_description' => 'University degree and professional certification documents',
                    'file_path' => '/uploads/applications/3/michael_johnson_certificates.pdf',
                    'extracted_texts' => 'Bachelor of Mathematics and Statistics, University of Papua New Guinea, Second Class Upper Division, 2020. Certificate in Data Analysis and Visualization, PNG Institute of Public Administration, 2021...',
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:37:00',
                    'updated_at' => '2024-01-12 14:37:00'
                ],
                [
                    'id' => 9,
                    'application_id' => 3,
                    'applicant_id' => 103,
                    'file_title' => 'Portfolio of Work',
                    'file_description' => 'Sample reports and data visualization projects',
                    'file_path' => '/uploads/applications/3/michael_johnson_portfolio.pdf',
                    'extracted_texts' => 'Portfolio includes: PNG Economic Indicators Dashboard, Government Budget Analysis Report, Population Demographics Visualization, Healthcare Statistics Analysis...',
                    'created_by' => 103,
                    'updated_by' => 103,
                    'created_at' => '2024-01-12 14:38:00',
                    'updated_at' => '2024-01-12 14:38:00'
                ]
            ],
            4 => [
                [
                    'id' => 5,
                    'application_id' => 4,
                    'file_type' => 'resume',
                    'file_name' => 'sarah_williams_cv.pdf',
                    'file_path' => '/uploads/applications/4/sarah_williams_cv.pdf',
                    'file_size' => '1.9MB',
                    'uploaded_at' => '2024-01-13 16:50:00'
                ],
                [
                    'id' => 6,
                    'application_id' => 4,
                    'file_type' => 'certificate',
                    'file_name' => 'diploma_certificate.pdf',
                    'file_path' => '/uploads/applications/4/diploma_certificate.pdf',
                    'file_size' => '0.8MB',
                    'uploaded_at' => '2024-01-13 16:52:00'
                ]
            ]
        ];

        return $files[$applicationId] ?? [];
    }



    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_pre_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        // Get application with related position and exercise data
        $application = $this->getMockApplicationDetails($id);

        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Fetch the corresponding applicant data
        $applicant = $this->getMockApplicant($application['applicant_id']);
        if (!$applicant) {
            // Handle case where applicant record is missing, though unlikely if application exists
            log_message('warning', 'Applicant record not found for application ID: ' . $id . ' and applicant ID: ' . $application['applicant_id']);
            $applicant = []; // Provide an empty array to avoid errors in the view
        }

        // Get related data for this application
        $experiences = $this->getMockExperiences($id);
        $education = $this->getMockEducation($id);
        $files = $this->getMockFiles($id);

        // Get position and exercise data based on application
        $position = [
            'id' => $application['position_id'],
            'designation' => 'Senior Software Engineer',
            'classification' => 'Professional',
            'location' => 'Port Moresby, NCD',
            'annual_salary' => 'K65,000 - K85,000',
            'position_reference' => 'IT-SR-001'
        ];

        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'advertisement_no' => 'ADV-2024-001',
            'gazzetted_no' => 'GAZ-2024-001'
        ];

        // Mock pre-screening criteria based on position requirements
        $preScreenCriteria = [
            [
                'name' => 'Educational Qualification',
                'description' => 'Bachelor\'s degree in Computer Science, Software Engineering, or related field',
                'required' => true,
                'weight' => 25
            ],
            [
                'name' => 'Work Experience',
                'description' => 'Minimum 5 years of professional software development experience',
                'required' => true,
                'weight' => 30
            ],
            [
                'name' => 'Technical Skills',
                'description' => 'Proficiency in modern programming languages (Java, Python, C#, etc.) and frameworks',
                'required' => true,
                'weight' => 25
            ],
            [
                'name' => 'Leadership Experience',
                'description' => 'Experience leading development teams or technical projects',
                'required' => false,
                'weight' => 15
            ],
            [
                'name' => 'Professional Certifications',
                'description' => 'Relevant industry certifications (AWS, Azure, Google Cloud, etc.)',
                'required' => false,
                'weight' => 5
            ]
        ];

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'applicant' => $applicant, // Pass applicant data to the view
            'position' => $position,
            'exercise' => $exercise,
            'preScreenCriteria' => $preScreenCriteria,
            'experiences' => $experiences,
            'education' => $education,
            'files' => $files,
            'educationModel' => null // Mock educationModel placeholder
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_pre_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        // Find the application first to ensure it exists
        $application = $this->getMockApplicationDetails($id);
        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Validate input
        $rules = [
            'status' => 'required|in_list[passed,failed,pending]',
            'remarks' => 'permit_empty|string|max_length[1000]'
        ];

        if ($this->request->getPost('status') === 'failed' && empty($this->request->getPost('remarks'))) {
            $rules['remarks'] = 'required|string|max_length[1000]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Process criteria results
        $criteriaResults = [];
        $criteriaIndices = $this->request->getPost('criteria_index') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];

        foreach ($criteriaIndices as $index => $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update (mock - not actually saving to database)
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id')
        ];

        // Mock successful update
        log_message('info', 'Mock pre-screening save for application ID: ' . $id . ' with data: ' . json_encode($data));

        return redirect()->to(base_url('application_pre_screening/show/' . $id))
            ->with('success', 'Pre-screening results saved successfully (mock mode).');
    }

    /**
     * [POST] Batch pre-screen multiple applications.
     * URI: /application_pre_screening/batch_update
     */
    public function batchUpdate()
    {
        $ids = $this->request->getPost('ids');
        $status = $this->request->getPost('status');
        $remarks = trim($this->request->getPost('remarks') ?? '');

        // --- Validation ---
        if (empty($ids) || !is_array($ids)) {
            $this->session->setFlashdata('error', 'No applications selected for batch update.');
            return redirect()->back();
        }

        $allowed_statuses = ['passed', 'failed', 'pending'];
        if (empty($status) || !in_array($status, $allowed_statuses)) {
            $this->session->setFlashdata('error', 'Invalid or missing status for batch update.');
            return redirect()->back()->withInput();
        }

        // Require remarks if status is 'failed'
        if ($status === 'failed' && empty($remarks)) {
             $this->session->setFlashdata('error', 'Remarks are required when batch failing applications.');
            return redirect()->back()->withInput();
        }

        // --- Mock batch update ---
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $status,
            'pre_screened_remarks' => "[Batch Update] " . $remarks,
            'pre_screened_criteria_results' => null,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id')
        ];

        $successCount = 0;
        $failCount = 0;

        foreach ($ids as $id) {
            $id = (int) $id;
            if ($id > 0) {
                try {
                    // Mock successful update
                    $successCount++;
                    log_message('info', 'Mock batch pre-screen for ID: ' . $id . ' with status: ' . $status);
                } catch (\Exception $e) {
                    log_message('error', 'Exception during batch pre-screening for ID ' . $id . ': ' . $e->getMessage());
                    $failCount++;
                }
            } else {
                $failCount++;
            }
        }

        // --- Set Flash Message ---
        $message = '';
        if ($successCount > 0) {
            $message .= "$successCount application(s) pre-screened successfully (mock mode). ";
        }
        if ($failCount > 0) {
            $message .= "$failCount application(s) failed to update.";
        }

        if ($successCount > 0 && $failCount == 0) {
            $this->session->setFlashdata('success', $message);
        } elseif ($failCount > 0) {
            $this->session->setFlashdata('warning', $message);
        } else {
             $this->session->setFlashdata('info', 'No applications were updated.');
        }

        return redirect()->back();
    }

    /**
     * [GET] Display exercises in selection status for pre-screening context.
     * URI: /application_pre_screening/exercises
     */
    public function exercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        $exercises = [
            [
                'id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV-2024-001',
                'exercise_year' => '2024',
                'department' => 'Information Technology',
                'status' => 'selection',
                'application_count' => 25,
                'publish_date_from' => '2024-01-10',
                'publish_date_to' => '2024-02-10',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-01',
                'advertisement_date' => '2024-01-05',
                'mode_of_advertisement' => 'Online and Print Media',
                'description' => 'Annual IT recruitment exercise for various technical positions',
                'created_at' => '2024-01-01 08:00:00'
            ],
            [
                'id' => 2,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'advertisement_no' => 'ADV-2024-002',
                'exercise_year' => '2024',
                'department' => 'Finance',
                'status' => 'selection',
                'application_count' => 18,
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-02-15',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-05',
                'advertisement_date' => '2024-01-10',
                'mode_of_advertisement' => 'Online and Print Media',
                'description' => 'Annual finance department recruitment exercise',
                'created_at' => '2024-01-05 09:00:00'
            ]
        ];

        $data = [
            'title' => 'Exercises Available for Pre-Screening',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/application_pre_screening_exercise_list', $data);
    }

    /**
     * [GET] Display all positions within an exercise for pre-screening.
     * URI: /application_pre_screening/exercise/{exerciseId}/positions
     */
    public function exercisePositions($exerciseId)
    {
        // Mock exercise data based on ExerciseModel structure
        $exercise = [
            'id' => $exerciseId,
            'org_id' => 1,
            'exercise_name' => $exerciseId == 1 ? 'IT Recruitment Exercise 2024' : 'Finance Department Recruitment 2024',
            'gazzetted_no' => 'GAZ-2024-00' . $exerciseId,
            'gazzetted_date' => '2024-01-01',
            'advertisement_no' => 'ADV-2024-00' . $exerciseId,
            'advertisement_date' => '2024-01-05',
            'mode_of_advertisement' => 'Online and Print Media',
            'publish_date_from' => '2024-01-10',
            'publish_date_to' => '2024-02-10',
            'description' => 'Annual recruitment exercise for various positions',
            'status' => 'selection',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock positions data based on PositionsModel structure - all positions in exercise
        $positions = [];
        if ($exerciseId == 1) { // IT Exercise
            $positions = [
                [
                    'id' => 1,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 1,
                    'position_group_name' => 'Technology',
                    'position_reference' => 'IT-SR-001',
                    'designation' => 'Senior Software Engineer',
                    'classification' => 'Professional',
                    'award' => 'IT Professional Award Level 3',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K65,000 - K85,000',
                    'qualifications' => 'Bachelor\'s degree in Computer Science or related field, minimum 5 years experience',
                    'knowledge' => 'Advanced programming languages, database management, system architecture',
                    'skills_competencies' => 'Leadership, project management, technical problem solving',
                    'job_experiences' => 'Minimum 5 years in software development, team leadership experience preferred',
                    'status' => 'active',
                    'applications_count' => 12,
                    'pending_prescreen_count' => 3,
                    'passed_prescreen_count' => 7,
                    'failed_prescreen_count' => 2,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 2,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 1,
                    'position_group_name' => 'Technology',
                    'position_reference' => 'IT-PM-002',
                    'designation' => 'IT Project Manager',
                    'classification' => 'Management',
                    'award' => 'Management Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K70,000 - K90,000',
                    'qualifications' => 'Bachelor\'s degree in IT/Business, PMP certification preferred, minimum 7 years experience',
                    'knowledge' => 'Project management methodologies, IT systems, stakeholder management',
                    'skills_competencies' => 'Leadership, communication, strategic planning, risk management',
                    'job_experiences' => 'Minimum 7 years in project management, IT project experience required',
                    'status' => 'active',
                    'applications_count' => 8,
                    'pending_prescreen_count' => 2,
                    'passed_prescreen_count' => 5,
                    'failed_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 3,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 1,
                    'position_group_name' => 'Technology',
                    'position_reference' => 'IT-DA-003',
                    'designation' => 'Data Analyst',
                    'classification' => 'Professional',
                    'award' => 'Data Professional Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K55,000 - K75,000',
                    'qualifications' => 'Bachelor\'s degree in Statistics, Mathematics, Computer Science or related field',
                    'knowledge' => 'Statistical analysis, data visualization, SQL, Python/R programming',
                    'skills_competencies' => 'Analytical thinking, attention to detail, communication',
                    'job_experiences' => 'Minimum 3 years in data analysis, experience with BI tools preferred',
                    'status' => 'active',
                    'applications_count' => 6,
                    'pending_prescreen_count' => 1,
                    'passed_prescreen_count' => 3,
                    'failed_prescreen_count' => 2,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 4,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 2,
                    'position_group_name' => 'Management',
                    'position_reference' => 'MG-DM-004',
                    'designation' => 'Department Manager',
                    'classification' => 'Executive',
                    'award' => 'Executive Award Level 1',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K90,000 - K120,000',
                    'qualifications' => 'Master\'s degree in Management/Business Administration, minimum 10 years experience',
                    'knowledge' => 'Strategic planning, organizational management, financial planning',
                    'skills_competencies' => 'Leadership, decision making, strategic thinking, team management',
                    'job_experiences' => 'Minimum 10 years in management roles, department leadership experience',
                    'status' => 'active',
                    'applications_count' => 5,
                    'pending_prescreen_count' => 1,
                    'passed_prescreen_count' => 3,
                    'failed_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 5,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 3,
                    'position_group_name' => 'Analytics',
                    'position_reference' => 'AN-BA-005',
                    'designation' => 'Business Analyst',
                    'classification' => 'Professional',
                    'award' => 'Business Professional Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K60,000 - K80,000',
                    'qualifications' => 'Bachelor\'s degree in Business, Economics or related field',
                    'knowledge' => 'Business process analysis, requirements gathering, stakeholder management',
                    'skills_competencies' => 'Analytical thinking, communication, problem solving',
                    'job_experiences' => 'Minimum 4 years in business analysis or related field',
                    'status' => 'active',
                    'applications_count' => 4,
                    'pending_prescreen_count' => 1,
                    'passed_prescreen_count' => 2,
                    'failed_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ]
            ];
        } else { // Finance Exercise
            $positions = [
                [
                    'id' => 6,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 4,
                    'position_group_name' => 'Finance',
                    'position_reference' => 'FN-AM-006',
                    'designation' => 'Accounting Manager',
                    'classification' => 'Management',
                    'award' => 'Finance Management Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K75,000 - K95,000',
                    'qualifications' => 'Bachelor\'s degree in Accounting/Finance, CPA certification preferred',
                    'knowledge' => 'Financial reporting, budgeting, tax compliance, audit procedures',
                    'skills_competencies' => 'Leadership, analytical thinking, attention to detail',
                    'job_experiences' => 'Minimum 7 years in accounting, management experience preferred',
                    'status' => 'active',
                    'applications_count' => 8,
                    'pending_prescreen_count' => 2,
                    'passed_prescreen_count' => 5,
                    'failed_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 7,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 4,
                    'position_group_name' => 'Finance',
                    'position_reference' => 'FN-FA-007',
                    'designation' => 'Financial Analyst',
                    'classification' => 'Professional',
                    'award' => 'Finance Professional Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K55,000 - K75,000',
                    'qualifications' => 'Bachelor\'s degree in Finance, Economics or related field',
                    'knowledge' => 'Financial modeling, investment analysis, risk assessment',
                    'skills_competencies' => 'Analytical thinking, Excel proficiency, communication',
                    'job_experiences' => 'Minimum 3 years in financial analysis or related field',
                    'status' => 'active',
                    'applications_count' => 4,
                    'pending_prescreen_count' => 1,
                    'passed_prescreen_count' => 2,
                    'failed_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 8,
                    'exercise_id' => $exerciseId,
                    'org_id' => 1,
                    'position_group_id' => 5,
                    'position_group_name' => 'Audit',
                    'position_reference' => 'AU-IA-008',
                    'designation' => 'Internal Auditor',
                    'classification' => 'Professional',
                    'award' => 'Audit Professional Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K60,000 - K80,000',
                    'qualifications' => 'Bachelor\'s degree in Accounting/Finance, CIA certification preferred',
                    'knowledge' => 'Audit procedures, risk assessment, compliance frameworks',
                    'skills_competencies' => 'Attention to detail, analytical thinking, communication',
                    'job_experiences' => 'Minimum 4 years in auditing or related field',
                    'status' => 'active',
                    'applications_count' => 6,
                    'pending_prescreen_count' => 2,
                    'passed_prescreen_count' => 3,
                    'failed_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ]
            ];
        }

        $data = [
            'title' => 'Positions in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'positions' => $positions
        ];

        return view('application_pre_screening/application_pre_screening_exercise_positions', $data);
    }









    /**
     * [GET] Display all applications for a specific position that need pre-screening.
     * URI: /application_pre_screening/position/{positionId}/applications
     */
    public function positionApplications($positionId)
    {
        $position = [
            'id' => $positionId,
            'designation' => 'Senior Software Engineer',
            'position_group_id' => 1,
            'group_name' => 'Technology',
            'exercise_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'classification' => 'Technical',
            'location' => 'Remote/HQ',
            'annual_salary' => '$80,000 - $120,000'
        ];

        $positionGroup = [
            'id' => 1,
            'group_name' => 'Technology'
        ];

        $exercise = [
            'id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024'
        ];

        // Mock applications data based on AppxApplicationDetailsModel structure
        $applications = [
            [
                'id' => 1,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 101,
                'position_id' => $positionId,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'date_of_birth' => '1990-05-15',
                'place_of_origin' => 'Port Moresby, NCD',
                'contact_details' => '+675 7123 4567',
                'location_address' => '123 Main Street, Port Moresby',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Tech Solutions PNG',
                'current_position' => 'Software Developer',
                'marital_status' => 'Single',
                'is_received' => 1,
                'received_status' => 'acknowledged',
                'received_at' => '2024-01-12 14:20:00',
                'application_status' => 'pending_prescreen',
                'pre_screened_status' => null,
                'pre_screened_at' => null,
                'pre_screened_by' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-10 09:30:00',
                'updated_at' => '2024-01-12 14:20:00',
                'recieved_acknowledged' => '2024-01-12 14:20:00'
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 102,
                'position_id' => $positionId,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'date_of_birth' => '1988-08-22',
                'place_of_origin' => 'Lae, Morobe',
                'contact_details' => '+675 7234 5678',
                'location_address' => '456 Oak Avenue, Lae',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Business Consultants Ltd',
                'current_position' => 'Senior Analyst',
                'marital_status' => 'Married',
                'is_received' => 1,
                'received_status' => 'acknowledged',
                'received_at' => '2024-01-13 10:45:00',
                'application_status' => 'pre_screened',
                'pre_screened_status' => 'passed',
                'pre_screened_at' => '2024-01-15 09:30:00',
                'pre_screened_by' => 1,
                'pre_screened_remarks' => 'Excellent qualifications and experience',
                'created_at' => '2024-01-11 11:15:00',
                'updated_at' => '2024-01-15 09:30:00',
                'recieved_acknowledged' => '2024-01-13 10:45:00'
            ],
            [
                'id' => 3,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 103,
                'position_id' => $positionId,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Michael',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'date_of_birth' => '1985-12-03',
                'place_of_origin' => 'Mount Hagen, WHP',
                'contact_details' => '+675 7345 6789',
                'location_address' => '789 Pine Road, Mount Hagen',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Highland Enterprises',
                'current_position' => 'Project Coordinator',
                'marital_status' => 'Married',
                'is_received' => 1,
                'received_status' => 'acknowledged',
                'received_at' => '2024-01-14 09:30:00',
                'application_status' => 'pre_screened',
                'pre_screened_status' => 'failed',
                'pre_screened_at' => '2024-01-16 14:15:00',
                'pre_screened_by' => 1,
                'pre_screened_remarks' => 'Does not meet minimum experience requirements',
                'created_at' => '2024-01-12 16:45:00',
                'updated_at' => '2024-01-16 14:15:00',
                'recieved_acknowledged' => '2024-01-14 09:30:00'
            ],
            [
                'id' => 4,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 104,
                'position_id' => $positionId,
                'application_number' => 'APP-2024-004',
                'first_name' => 'Sarah',
                'last_name' => 'Wilson',
                'gender' => 'Female',
                'date_of_birth' => '1992-03-18',
                'place_of_origin' => 'Madang, Madang',
                'contact_details' => '+675 7456 7890',
                'location_address' => '321 Beach Street, Madang',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Coastal Development Corp',
                'current_position' => 'Administrative Officer',
                'marital_status' => 'Single',
                'is_received' => 1,
                'received_status' => 'acknowledged',
                'received_at' => '2024-01-15 13:10:00',
                'application_status' => 'pending_prescreen',
                'pre_screened_status' => null,
                'pre_screened_at' => null,
                'pre_screened_by' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-13 08:20:00',
                'updated_at' => '2024-01-15 13:10:00',
                'recieved_acknowledged' => '2024-01-15 13:10:00'
            ],
            [
                'id' => 5,
                'org_id' => 1,
                'exercise_id' => 1,
                'applicant_id' => 105,
                'position_id' => $positionId,
                'application_number' => 'APP-2024-005',
                'first_name' => 'Alex',
                'last_name' => 'Brown',
                'gender' => 'Male',
                'date_of_birth' => '1987-09-25',
                'place_of_origin' => 'Vanimo, Sandaun',
                'contact_details' => '+675 7567 8901',
                'location_address' => '654 River Lane, Vanimo',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Border Services PNG',
                'current_position' => 'Operations Manager',
                'marital_status' => 'Married',
                'is_received' => 1,
                'received_status' => 'acknowledged',
                'received_at' => '2024-01-16 11:20:00',
                'application_status' => 'pending_prescreen',
                'pre_screened_status' => null,
                'pre_screened_at' => null,
                'pre_screened_by' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-14 11:20:00',
                'updated_at' => '2024-01-16 11:20:00',
                'recieved_acknowledged' => '2024-01-16 11:20:00'
            ]
        ];

        $data = [
            'title' => 'Applications for Position: ' . esc($position['designation']),
            'menu' => 'applications',
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'applications' => $applications
        ];

        return view('application_pre_screening/position_applications', $data);
    }


}