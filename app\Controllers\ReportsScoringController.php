<?php

namespace App\Controllers;

use CodeIgniter\Controller;

/**
 * ReportsScoringController
 * 
 * Controller for scoring reports including application scoring and interview scoring reports
 */
class ReportsScoringController extends Controller
{
    public function __construct()
    {
        helper(['url', 'form']);
    }

    /**
     * [GET] Application Scoring Report
     * URI: /reports/scoring/{positionId}
     */
    public function scoring($positionId)
    {
        // Mock position data
        $position = $this->getMockPosition($positionId);
        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Mock scoring data based on AppxApplicationRatingModel and RateItemsScoresModel
        $scoringData = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'contact_details' => '{"phone": "71234567", "email": "<EMAIL>"}',
                'scoring_details' => [
                    ['criteria' => 'Educational Qualifications', 'max_score' => 25, 'score' => 20, 'percentage' => 80],
                    ['criteria' => 'Work Experience', 'max_score' => 30, 'score' => 25, 'percentage' => 83.33],
                    ['criteria' => 'Technical Skills', 'max_score' => 25, 'score' => 18, 'percentage' => 72],
                    ['criteria' => 'Professional Certifications', 'max_score' => 20, 'score' => 15, 'percentage' => 75]
                ],
                'total_score' => 78,
                'max_total_score' => 100,
                'overall_percentage' => 78,
                'rating' => 'Good',
                'rated_by' => 'Rating Officer 1',
                'rated_at' => '2024-02-10 10:30:00',
                'remarks' => 'Strong technical background with good experience.'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Mary',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'contact_details' => '{"phone": "72345678", "email": "<EMAIL>"}',
                'scoring_details' => [
                    ['criteria' => 'Educational Qualifications', 'max_score' => 25, 'score' => 25, 'percentage' => 100],
                    ['criteria' => 'Work Experience', 'max_score' => 30, 'score' => 28, 'percentage' => 93.33],
                    ['criteria' => 'Technical Skills', 'max_score' => 25, 'score' => 22, 'percentage' => 88],
                    ['criteria' => 'Professional Certifications', 'max_score' => 20, 'score' => 18, 'percentage' => 90]
                ],
                'total_score' => 93,
                'max_total_score' => 100,
                'overall_percentage' => 93,
                'rating' => 'Excellent',
                'rated_by' => 'Rating Officer 2',
                'rated_at' => '2024-02-10 14:15:00',
                'remarks' => 'Outstanding qualifications and extensive experience. Highly recommended.'
            ],
            [
                'id' => 3,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Peter',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'contact_details' => '{"phone": "73456789", "email": "<EMAIL>"}',
                'scoring_details' => [
                    ['criteria' => 'Educational Qualifications', 'max_score' => 25, 'score' => 18, 'percentage' => 72],
                    ['criteria' => 'Work Experience', 'max_score' => 30, 'score' => 20, 'percentage' => 66.67],
                    ['criteria' => 'Technical Skills', 'max_score' => 25, 'score' => 15, 'percentage' => 60],
                    ['criteria' => 'Professional Certifications', 'max_score' => 20, 'score' => 10, 'percentage' => 50]
                ],
                'total_score' => 63,
                'max_total_score' => 100,
                'overall_percentage' => 63,
                'rating' => 'Satisfactory',
                'rated_by' => 'Rating Officer 1',
                'rated_at' => '2024-02-11 09:20:00',
                'remarks' => 'Meets basic requirements but lacks advanced certifications.'
            ]
        ];

        $data = [
            'title' => 'Application Scoring Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'scoring_data' => $scoringData
        ];

        return view('application_reports/appx_reports_scoring', $data);
    }

    /**
     * [GET] Interview Scoring Report
     * URI: /reports/interview-scoring/{positionId}
     */
    public function interviewScoring($positionId)
    {
        // Mock position data
        $position = $this->getMockPosition($positionId);
        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Mock interview scoring data
        $interviewScoringData = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'contact_details' => '{"phone": "71234567", "email": "<EMAIL>"}',
                'interview_date' => '2024-02-15',
                'interview_time' => '09:00',
                'interview_panel' => 'Panel A',
                'interview_scores' => [
                    ['criteria' => 'Technical Knowledge', 'max_score' => 25, 'score' => 20, 'percentage' => 80],
                    ['criteria' => 'Communication Skills', 'max_score' => 20, 'score' => 17, 'percentage' => 85],
                    ['criteria' => 'Problem Solving', 'max_score' => 25, 'score' => 19, 'percentage' => 76],
                    ['criteria' => 'Leadership Potential', 'max_score' => 15, 'score' => 12, 'percentage' => 80],
                    ['criteria' => 'Cultural Fit', 'max_score' => 15, 'score' => 13, 'percentage' => 86.67]
                ],
                'total_interview_score' => 81,
                'max_interview_score' => 100,
                'interview_percentage' => 81,
                'application_score' => 78,
                'combined_score' => 79.5,
                'final_ranking' => 2,
                'interview_status' => 'completed',
                'interviewer_remarks' => 'Good technical knowledge and communication skills. Shows potential for growth.',
                'interviewed_by' => 'Interview Panel A',
                'interviewed_at' => '2024-02-15 09:00:00'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Mary',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'contact_details' => '{"phone": "72345678", "email": "<EMAIL>"}',
                'interview_date' => '2024-02-15',
                'interview_time' => '10:30',
                'interview_panel' => 'Panel A',
                'interview_scores' => [
                    ['criteria' => 'Technical Knowledge', 'max_score' => 25, 'score' => 24, 'percentage' => 96],
                    ['criteria' => 'Communication Skills', 'max_score' => 20, 'score' => 19, 'percentage' => 95],
                    ['criteria' => 'Problem Solving', 'max_score' => 25, 'score' => 23, 'percentage' => 92],
                    ['criteria' => 'Leadership Potential', 'max_score' => 15, 'score' => 14, 'percentage' => 93.33],
                    ['criteria' => 'Cultural Fit', 'max_score' => 15, 'score' => 14, 'percentage' => 93.33]
                ],
                'total_interview_score' => 94,
                'max_interview_score' => 100,
                'interview_percentage' => 94,
                'application_score' => 93,
                'combined_score' => 93.5,
                'final_ranking' => 1,
                'interview_status' => 'completed',
                'interviewer_remarks' => 'Exceptional candidate with outstanding technical knowledge and leadership qualities. Highly recommended.',
                'interviewed_by' => 'Interview Panel A',
                'interviewed_at' => '2024-02-15 10:30:00'
            ]
        ];

        $data = [
            'title' => 'Interview Scoring Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'interview_scoring_data' => $interviewScoringData
        ];

        return view('application_reports/appx_reports_interview_scoring', $data);
    }

    /**
     * [POST] Export Scoring Report
     * URI: /reports/scoring/export
     */
    public function exportScoring()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Application scoring report exported successfully',
            'file_url' => base_url('exports/application_scoring_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * [POST] Export Interview Scoring Report
     * URI: /reports/interview-scoring/export
     */
    public function exportInterviewScoring()
    {
        // Mock export response for UI development
        $response = [
            'success' => true,
            'message' => 'Interview scoring report exported successfully',
            'file_url' => base_url('exports/interview_scoring_' . date('Y-m-d_H-i-s') . '.xlsx')
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Get mock position data
     */
    private function getMockPosition($positionId)
    {
        $positions = [
            1 => [
                'id' => 1,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Engineer',
                'classification' => 'Grade 12',
                'award' => 'K85,000 - K95,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            2 => [
                'id' => 2,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'classification' => 'Grade 11',
                'award' => 'K75,000 - K85,000',
                'location' => 'Port Moresby',
                'group_name' => 'Information Technology',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ],
            3 => [
                'id' => 3,
                'exercise_id' => 1,
                'position_group_id' => 2,
                'position_reference' => 'SYS-001',
                'designation' => 'System Administrator',
                'classification' => 'Grade 10',
                'award' => 'K65,000 - K75,000',
                'location' => 'Port Moresby',
                'group_name' => 'System Administration',
                'exercise_name' => 'IT Department Recruitment Exercise 2024'
            ]
        ];

        return $positions[$positionId] ?? null;
    }
}
