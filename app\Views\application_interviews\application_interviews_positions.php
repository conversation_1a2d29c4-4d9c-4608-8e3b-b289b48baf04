<?php
/**
 * Interview Session Positions
 * Manage positions within an interview session
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($session['session_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Session Positions</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-calendar-alt me-1"></i>
                        <?= esc($session['session_name']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Sessions
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPositionModal">
                        <i class="fas fa-plus me-2"></i>Add Position
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Session Info Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-calendar-alt text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Session Period</h6>
                        <small class="text-muted">
                            <?= date('M d, Y', strtotime($session['start_date'])) ?><br>
                            to <?= date('M d, Y', strtotime($session['end_date'])) ?>
                        </small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-briefcase text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Positions</h6>
                        <span class="fw-bold"><?= count($positions) ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-users text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Total Applicants</h6>
                        <span class="fw-bold"><?= array_sum(array_column($positions, 'applicants_count')) ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="fas fa-check-circle text-info mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Shortlisted</h6>
                        <span class="fw-bold"><?= array_sum(array_column($positions, 'shortlisted_count')) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions in Session -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase text-primary me-2"></i>
                        Positions in Session
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-secondary">
                        <?= count($positions) ?> Position(s)
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-briefcase text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Positions Added</h5>
                    <p class="text-muted">Add positions to this interview session to start managing interviews.</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPositionModal">
                        <i class="fas fa-plus me-2"></i>Add Position
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Position Details</th>
                                <th>Classification</th>
                                <th>Location</th>
                                <th>Salary Range</th>
                                <th>Applicants</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?= esc($position['designation']) ?></h6>
                                            <small class="text-muted">
                                                <i class="fas fa-tag me-1"></i>
                                                <?= esc($position['position_reference']) ?>
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-layer-group me-1"></i>
                                                <?= esc($position['group_name']) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-dark">
                                            <?= esc($position['classification']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                        <?= esc($position['location']) ?>
                                    </td>
                                    <td>
                                        <div class="fw-medium"><?= esc($position['annual_salary']) ?></div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary me-2">
                                                <?= $position['applicants_count'] ?> Total
                                            </span>
                                            <span class="badge bg-success">
                                                <?= $position['shortlisted_count'] ?> Shortlisted
                                            </span>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('interviews/position/' . $position['id'] . '/questions') ?>" 
                                               class="btn btn-primary btn-sm" 
                                               title="Manage Questions">
                                                <i class="fas fa-question-circle"></i>
                                            </a>
                                            <a href="<?= base_url('interviews/position/' . $position['id'] . '/interviewees') ?>" 
                                               class="btn btn-info btn-sm" 
                                               title="View Interviewees">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <a href="<?= base_url('interviews/position/' . $position['id'] . '/scoring') ?>" 
                                               class="btn btn-success btn-sm" 
                                               title="Interview Scoring">
                                                <i class="fas fa-star"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-danger btn-sm remove-position-btn" 
                                                    data-position-id="<?= $position['id'] ?>"
                                                    data-position-name="<?= esc($position['designation']) ?>"
                                                    title="Remove from Session">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1" aria-labelledby="addPositionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addPositionModalLabel">
                    <i class="fas fa-plus me-2"></i>Add Position to Session
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (empty($available_positions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5 class="text-muted">No Available Positions</h5>
                        <p class="text-muted">All positions from this exercise have been added to interview sessions.</p>
                    </div>
                <?php else: ?>
                    <?= form_open('interviews/session/' . $session['id'] . '/add-position', ['id' => 'addPositionForm']) ?>
                        <div class="mb-3">
                            <label for="position_id" class="form-label">Select Position</label>
                            <select class="form-select" id="position_id" name="position_id" required>
                                <option value="">Choose a position...</option>
                                <?php foreach ($available_positions as $position): ?>
                                    <option value="<?= $position['id'] ?>">
                                        <?= esc($position['designation']) ?> 
                                        (<?= esc($position['position_reference']) ?>) - 
                                        <?= esc($position['location']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>Position</th>
                                        <th>Reference</th>
                                        <th>Classification</th>
                                        <th>Location</th>
                                        <th>Group</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($available_positions as $position): ?>
                                        <tr>
                                            <td><?= esc($position['designation']) ?></td>
                                            <td><?= esc($position['position_reference']) ?></td>
                                            <td>
                                                <span class="badge bg-info text-dark">
                                                    <?= esc($position['classification']) ?>
                                                </span>
                                            </td>
                                            <td><?= esc($position['location']) ?></td>
                                            <td><?= esc($position['group_name']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?= form_close() ?>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <?php if (!empty($available_positions)): ?>
                    <button type="submit" form="addPositionForm" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Position
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle add position form
    const addPositionForm = document.getElementById('addPositionForm');
    if (addPositionForm) {
        addPositionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    // Close modal and reload page
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addPositionModal'));
                    modal.hide();
                    window.location.reload();
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toastr !== 'undefined') {
                    toastr.error('An error occurred while adding the position');
                } else {
                    alert('An error occurred while adding the position');
                }
            });
        });
    }

    // Handle remove position
    document.querySelectorAll('.remove-position-btn').forEach(button => {
        button.addEventListener('click', function() {
            const positionId = this.getAttribute('data-position-id');
            const positionName = this.getAttribute('data-position-name');
            
            if (confirm(`Are you sure you want to remove "${positionName}" from this interview session?`)) {
                fetch(`<?= base_url('interviews/session/' . $session['id'] . '/remove-position/') ?>${positionId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (typeof toastr !== 'undefined') {
                            toastr.success(data.message);
                        } else {
                            alert(data.message);
                        }
                        window.location.reload();
                    } else {
                        if (typeof toastr !== 'undefined') {
                            toastr.error(data.message);
                        } else {
                            alert('Error: ' + data.message);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (typeof toastr !== 'undefined') {
                        toastr.error('An error occurred while removing the position');
                    } else {
                        alert('An error occurred while removing the position');
                    }
                });
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
