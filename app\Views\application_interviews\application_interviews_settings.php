<?php
/**
 * Interview Settings
 * Configure interview settings for a session
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/' . $exercise['id']) ?>"><?= esc($exercise['exercise_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/session/' . $session['id'] . '/positions') ?>"><?= esc($session['session_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Settings</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Settings</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-calendar-alt me-1"></i>
                        <?= esc($session['session_name']) ?>
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/session/' . $session['id'] . '/positions') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Interview Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('interviews/session/' . $session['id'] . '/settings/save', ['id' => 'settingsForm']) ?>
                        
                        <!-- Timing Settings -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-clock me-2"></i>Timing Settings
                                </h6>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="interview_duration" class="form-label">
                                    Interview Duration (minutes) <span class="text-danger">*</span>
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="interview_duration" 
                                       name="interview_duration" 
                                       value="<?= $settings['interview_duration'] ?>"
                                       min="15" 
                                       max="240" 
                                       step="15" 
                                       required>
                            </div>

                            <div class="col-md-4">
                                <label for="break_duration" class="form-label">
                                    Break Duration (minutes)
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="break_duration" 
                                       name="break_duration" 
                                       value="<?= $settings['break_duration'] ?>"
                                       min="5" 
                                       max="60" 
                                       step="5">
                            </div>

                            <div class="col-md-2">
                                <label for="start_time" class="form-label">
                                    Start Time <span class="text-danger">*</span>
                                </label>
                                <input type="time" 
                                       class="form-control" 
                                       id="start_time" 
                                       name="start_time" 
                                       value="<?= $settings['start_time'] ?>"
                                       required>
                            </div>

                            <div class="col-md-2">
                                <label for="end_time" class="form-label">
                                    End Time <span class="text-danger">*</span>
                                </label>
                                <input type="time" 
                                       class="form-control" 
                                       id="end_time" 
                                       name="end_time" 
                                       value="<?= $settings['end_time'] ?>"
                                       required>
                            </div>
                        </div>

                        <!-- Interview Format -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-users me-2"></i>Interview Format
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label for="interview_type" class="form-label">
                                    Interview Type <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="interview_type" name="interview_type" required>
                                    <option value="panel" <?= $settings['interview_type'] === 'panel' ? 'selected' : '' ?>>Panel Interview</option>
                                    <option value="individual" <?= $settings['interview_type'] === 'individual' ? 'selected' : '' ?>>Individual Interview</option>
                                    <option value="group" <?= $settings['interview_type'] === 'group' ? 'selected' : '' ?>>Group Interview</option>
                                    <option value="technical" <?= $settings['interview_type'] === 'technical' ? 'selected' : '' ?>>Technical Assessment</option>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="panel_size" class="form-label">
                                    Panel Size
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="panel_size" 
                                       name="panel_size" 
                                       value="<?= $settings['panel_size'] ?>"
                                       min="1" 
                                       max="10">
                                <div class="form-text">Number of interviewers in the panel</div>
                            </div>
                        </div>

                        <!-- Scoring Settings -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-star me-2"></i>Scoring Settings
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label for="scoring_method" class="form-label">
                                    Scoring Method <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="scoring_method" name="scoring_method" required>
                                    <option value="weighted" <?= $settings['scoring_method'] === 'weighted' ? 'selected' : '' ?>>Weighted Average</option>
                                    <option value="simple" <?= $settings['scoring_method'] === 'simple' ? 'selected' : '' ?>>Simple Average</option>
                                    <option value="total" <?= $settings['scoring_method'] === 'total' ? 'selected' : '' ?>>Total Score</option>
                                </select>
                            </div>
                        </div>

                        <!-- Notification Settings -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-bell me-2"></i>Notification Settings
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="notification_enabled" 
                                           name="notification_enabled" 
                                           value="1"
                                           <?= $settings['notification_enabled'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="notification_enabled">
                                        Enable Email Notifications
                                    </label>
                                </div>
                                <div class="form-text">Send interview invitations and reminders via email</div>
                            </div>

                            <div class="col-md-6">
                                <label for="reminder_days" class="form-label">
                                    Reminder Days Before Interview
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="reminder_days" 
                                       name="reminder_days" 
                                       value="<?= $settings['reminder_days'] ?>"
                                       min="1" 
                                       max="14">
                                <div class="form-text">Days before interview to send reminder</div>
                            </div>
                        </div>

                        <!-- Venue and Instructions -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-map-marker-alt me-2"></i>Venue and Instructions
                                </h6>
                            </div>

                            <div class="col-12">
                                <label for="venue" class="form-label">
                                    Interview Venue <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="venue" 
                                       name="venue" 
                                       value="<?= esc($settings['venue']) ?>"
                                       placeholder="e.g., Conference Room A, Government Building"
                                       required>
                            </div>

                            <div class="col-12">
                                <label for="special_instructions" class="form-label">
                                    Special Instructions
                                </label>
                                <textarea class="form-control" 
                                          id="special_instructions" 
                                          name="special_instructions" 
                                          rows="4"
                                          placeholder="Any special instructions for candidates"><?= esc($settings['special_instructions']) ?></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('interviews/session/' . $session['id'] . '/positions') ?>" 
                                       class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="saveBtn">
                                        <i class="fas fa-save me-2"></i>Save Settings
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Settings Summary -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Settings Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Session:</strong><br>
                        <span class="text-muted"><?= esc($session['session_name']) ?></span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Duration:</strong><br>
                        <span class="text-muted"><?= date('M d', strtotime($session['start_date'])) ?> - <?= date('M d, Y', strtotime($session['end_date'])) ?></span>
                    </div>

                    <div class="mb-3">
                        <strong>Current Settings:</strong><br>
                        <ul class="list-unstyled small text-muted">
                            <li><i class="fas fa-clock me-1"></i> <?= $settings['interview_duration'] ?> minutes per interview</li>
                            <li><i class="fas fa-pause me-1"></i> <?= $settings['break_duration'] ?> minutes break</li>
                            <li><i class="fas fa-users me-1"></i> <?= ucfirst($settings['interview_type']) ?> format</li>
                            <li><i class="fas fa-star me-1"></i> <?= ucfirst($settings['scoring_method']) ?> scoring</li>
                            <li><i class="fas fa-bell me-1"></i> Notifications: <?= $settings['notification_enabled'] ? 'Enabled' : 'Disabled' ?></li>
                        </ul>
                    </div>

                    <div class="alert alert-info small">
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>Tip:</strong> These settings will apply to all interviews in this session. You can modify them anytime before generating the schedule.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('settingsForm');
    const saveBtn = document.getElementById('saveBtn');

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Disable save button
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

        // Submit form data
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                if (typeof toastr !== 'undefined') {
                    toastr.success(data.message);
                } else {
                    alert(data.message);
                }
            } else {
                // Show error message
                if (typeof toastr !== 'undefined') {
                    toastr.error(data.message || 'Failed to save settings');
                } else {
                    alert('Error: ' + (data.message || 'Failed to save settings'));
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('An error occurred while saving settings');
            } else {
                alert('An error occurred while saving settings');
            }
        })
        .finally(() => {
            // Re-enable save button
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>Save Settings';
        });
    });

    // Validate time inputs
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');

    function validateTimes() {
        if (startTimeInput.value && endTimeInput.value) {
            if (endTimeInput.value <= startTimeInput.value) {
                endTimeInput.setCustomValidity('End time must be after start time');
            } else {
                endTimeInput.setCustomValidity('');
            }
        }
    }

    startTimeInput.addEventListener('change', validateTimes);
    endTimeInput.addEventListener('change', validateTimes);
});
</script>
<?= $this->endSection() ?>
