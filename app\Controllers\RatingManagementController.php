<?php

namespace App\Controllers;

class RatingManagementController extends BaseController
{
    public function __construct()
    {
        // Model references removed for UI development
    }

    /**
     * Display a listing of the exercises available for rating
     */
    public function index()
    {
        // Mock exercises data in selection status - using only dummy data
        $exercises = [
            [
                'id' => 1,
                'exercise_name' => 'Software Development Recruitment 2024',
                'exercise_code' => 'SDR-2024-001',
                'department' => 'Information Technology',
                'status' => 'selection',
                'created_at' => '2024-01-01 08:00:00'
            ],
            [
                'id' => 2,
                'exercise_name' => 'Administrative Staff Recruitment 2024',
                'exercise_code' => 'ASR-2024-002',
                'department' => 'Administration',
                'status' => 'selection',
                'created_at' => '2024-01-05 09:00:00'
            ],
            [
                'id' => 3,
                'exercise_name' => 'Healthcare Professional Recruitment 2024',
                'exercise_code' => 'HPR-2024-003',
                'department' => 'Health Services',
                'status' => 'selection',
                'created_at' => '2024-01-10 10:00:00'
            ]
        ];

        $data = [
            'title' => 'Rating - Exercises',
            'menu' => 'rating',
            'exercises' => $exercises
        ];

       return view('rating/rating_exercise_list', $data);
    }

    /**
     * Display position groups for a specific exercise
     */
    public function positionGroups($exerciseId)
    {
        // Mock exercise data
        $exercise = [
            'id' => $exerciseId,
            'exercise_name' => 'Software Development Recruitment 2024',
            'status' => 'selection',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock position groups data
        $positionGroups = [
            [
                'id' => 1,
                'group_name' => 'Technical Positions',
                'exercise_id' => $exerciseId,
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'group_name' => 'Management Positions',
                'exercise_id' => $exerciseId,
                'created_at' => '2024-01-10 10:00:00'
            ]
        ];

        $data = [
            'title' => 'Rating - Position Groups',
            'menu' => 'rating',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('rating/rating_position_groups', $data);
    }



    /**
     * Display positions for a specific exercise (GET)
     * This shows positions with their group references in a table
     */
    public function positions($exerciseId)
    {
        // Mock exercise data
        $exercise = [
            'id' => $exerciseId,
            'exercise_name' => $exerciseId == 1 ? 'IT Recruitment Exercise 2024' : ($exerciseId == 2 ? 'Finance Department Recruitment 2024' : 'Administrative Services Recruitment 2024'),
            'status' => 'selection',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock positions data with group references
        $positions = [
            [
                'id' => 1,
                'designation' => 'Senior Software Developer',
                'position_reference' => 'DEV001',
                'group_name' => 'Technical Positions',
                'requirements' => 'Bachelor\'s degree in Computer Science, 5+ years experience',
                'created_at' => '2024-01-01 08:00:00',
                'application_count' => 15
            ],
            [
                'id' => 2,
                'designation' => 'Database Administrator',
                'position_reference' => 'DBA001',
                'group_name' => 'Technical Positions',
                'requirements' => 'Bachelor\'s degree in IT, 3+ years DBA experience',
                'created_at' => '2024-01-01 08:00:00',
                'application_count' => 8
            ],
            [
                'id' => 3,
                'designation' => 'Administrative Officer',
                'position_reference' => 'ADM001',
                'group_name' => 'Administrative Positions',
                'requirements' => 'Bachelor\'s degree, 2+ years administrative experience',
                'created_at' => '2024-01-01 08:00:00',
                'application_count' => 22
            ],
            [
                'id' => 4,
                'designation' => 'Project Manager',
                'position_reference' => 'PM001',
                'group_name' => 'Management Positions',
                'requirements' => 'Bachelor\'s degree, PMP certification preferred, 5+ years experience',
                'created_at' => '2024-01-01 08:00:00',
                'application_count' => 12
            ]
        ];

        $data = [
            'title' => 'Rating - Positions',
            'menu' => 'rating',
            'exercise' => $exercise,
            'positions' => $positions
        ];

        return view('rating/rating_positions', $data);
    }

    /**
     * Display applications for a specific position
     */
    public function applications($positionId)
    {
        // Mock data for position
        $position = [
            'id' => $positionId,
            'designation' => 'Senior Software Engineer',
            'position_group_id' => 1,
            'requirements' => 'Bachelor\'s degree in Computer Science, 5+ years experience',
            'created_at' => '2024-01-15 10:00:00'
        ];

        // Mock data for position group
        $positionGroup = [
            'id' => 1,
            'group_name' => 'Technical Positions',
            'exercise_id' => 1,
            'created_at' => '2024-01-10 09:00:00'
        ];

        // Mock data for exercise
        $exercise = [
            'id' => 1,
            'exercise_name' => 'Software Development Recruitment 2024',
            'status' => 'selection',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock applications data that have passed pre-screening
        $applications = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'Gloria',
                'last_name' => 'Pore',
                'position_id' => $positionId,
                'pre_screened_status' => 'passed',
                'profile_status' => 'completed',
                'rating_status' => null,
                'created_at' => '2024-01-20 14:30:00'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'first_name' => 'John',
                'last_name' => 'Smith',
                'position_id' => $positionId,
                'pre_screened_status' => 'passed',
                'profile_status' => 'completed',
                'rating_status' => 'completed',
                'created_at' => '2024-01-21 09:15:00'
            ],
            [
                'id' => 3,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'position_id' => $positionId,
                'pre_screened_status' => 'passed',
                'profile_status' => 'completed',
                'rating_status' => null,
                'created_at' => '2024-01-22 11:45:00'
            ]
        ];

        $data = [
            'title' => 'Rating - Applications',
            'menu' => 'rating',
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'applications' => $applications
        ];

        return view('rating/rating_applications', $data);
    }

    /**
     * Display rating form for a specific application (GET)
     */
    public function rate($applicationId)
    {
        // Mock application data based on AppxApplicationDetailsModel structure
        $application = [
            'id' => $applicationId,
            'application_number' => 'APP-2024-001',
            'first_name' => 'Gloria',
            'last_name' => 'Pore',
            'gender' => 'Female',
            'date_of_birth' => '1990-05-15',
            'place_of_origin' => 'Nairobi, Kenya',
            'contact_details' => '+254712345678',
            'location_address' => 'Westlands, Nairobi',
            'current_employer' => 'Tech Solutions Ltd',
            'current_position' => 'Software Developer',
            'current_salary' => '120000',
            'citizenship' => 'Kenyan',
            'marital_status' => 'Single',
            'position_id' => 1,
            'exercise_id' => 1,
            'pre_screened_status' => 'passed',
            'profile_status' => 'completed',
            'rating_status' => null,
            'created_at' => '2024-01-20 14:30:00'
        ];

        // Mock position data
        $position = [
            'id' => 1,
            'designation' => 'Senior Software Engineer',
            'position_group_id' => 1,
            'requirements' => 'Bachelor\'s degree in Computer Science, 5+ years experience'
        ];

        // Mock position group data
        $positionGroup = [
            'id' => 1,
            'group_name' => 'Technical Positions',
            'exercise_id' => 1
        ];

        // Mock exercise data
        $exercise = [
            'id' => 1,
            'exercise_name' => 'Software Development Recruitment 2024',
            'status' => 'selection'
        ];

        // Mock rating criteria based on AppxApplicationDetailsModel rating fields
        $ratingCriteria = [
            [
                'id' => 1,
                'name' => 'Age',
                'field' => 'rating_age',
                'max_score' => 5,
                'current_score' => 0,
                'description' => 'Age appropriateness for the position'
            ],
            [
                'id' => 2,
                'name' => 'Education Qualification',
                'field' => 'rating_qualification',
                'max_score' => 25,
                'current_score' => 0,
                'description' => 'Educational background and qualifications'
            ],
            [
                'id' => 3,
                'name' => 'Private Sector Experience (Relevant)',
                'field' => 'rating_experience_private_relevant',
                'max_score' => 20,
                'current_score' => 0,
                'description' => 'Relevant experience in private sector'
            ],
            [
                'id' => 4,
                'name' => 'Public Sector Experience (Relevant)',
                'field' => 'rating_experience_public_relevant',
                'max_score' => 15,
                'current_score' => 0,
                'description' => 'Relevant experience in public sector'
            ],
            [
                'id' => 5,
                'name' => 'Skills and Competencies',
                'field' => 'rating_skills_competencies',
                'max_score' => 15,
                'current_score' => 0,
                'description' => 'Technical and soft skills assessment'
            ],
            [
                'id' => 6,
                'name' => 'Knowledge',
                'field' => 'rating_knowledge',
                'max_score' => 10,
                'current_score' => 0,
                'description' => 'Domain knowledge and expertise'
            ],
            [
                'id' => 7,
                'name' => 'Public Service Ethics',
                'field' => 'rating_public_service',
                'max_score' => 5,
                'current_score' => 0,
                'description' => 'Understanding of public service values'
            ],
            [
                'id' => 8,
                'name' => 'Capability',
                'field' => 'rating_capability',
                'max_score' => 5,
                'current_score' => 0,
                'description' => 'Overall capability assessment'
            ]
        ];

        $data = [
            'title' => 'Rate Application - ' . $application['first_name'] . ' ' . $application['last_name'],
            'menu' => 'rating',
            'application' => $application,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'ratingCriteria' => $ratingCriteria
        ];

        return view('application_rating/application_rating_form', $data);
    }

    /**
     * Handle rating form submission (POST)
     */
    public function submitRating()
    {
        $applicationId = $this->request->getPost('application_id');
        $ratings = $this->request->getPost('ratings');
        $remarks = $this->request->getPost('remarks');

        // Mock validation and processing
        if (empty($applicationId) || empty($ratings)) {
            return redirect()->back()->with('error', 'Please provide all required rating information.');
        }

        // Mock processing - in real implementation, this would save to database
        $totalScore = 0;
        $maxTotalScore = 100;

        foreach ($ratings as $criteriaId => $score) {
            $totalScore += (int)$score;
        }

        // Mock success response
        $successMessage = "Rating submitted successfully! Total Score: {$totalScore}/{$maxTotalScore}";

        // Redirect back to applications list
        $positionId = $this->request->getPost('position_id') ?? 1;
        return redirect()->to("/rating/applications/{$positionId}")
                        ->with('success', $successMessage);
    }
}
