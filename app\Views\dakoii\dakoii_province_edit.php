<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/province/list') ?>" class="text-light-text">Provinces</a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Edit Province</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> Edit Province: <?= esc($province['name']) ?></h5>
                    <a href="<?= base_url('dakoii/province/list') ?>" class="btn btn-dark btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to Provinces
                    </a>
                </div>
                <div class="card-body">
                    <?= form_open('dakoii/province/update', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                    
                    <input type="hidden" name="id" value="<?= $province['id'] ?>">
                    <input type="hidden" name="country_id" value="<?= $province['country_id'] ?>">

                    <div class="mb-3">
                        <label for="provincecode" class="form-label">Province Code <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control" 
                               id="provincecode" 
                               name="provincecode" 
                               value="<?= old('provincecode', $province['provincecode']) ?>"
                               maxlength="10" 
                               required>
                        <div class="form-text">Enter a unique province code (max 10 characters)</div>
                        <div class="invalid-feedback">
                            Please provide a valid province code.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">Province Name <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control" 
                               id="name" 
                               name="name" 
                               value="<?= old('name', $province['name']) ?>"
                               maxlength="100" 
                               required>
                        <div class="form-text">Enter the full province name (max 100 characters)</div>
                        <div class="invalid-feedback">
                            Please provide a valid province name (minimum 3 characters).
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="country_id_display" class="form-label">Country</label>
                        <input type="text" 
                               class="form-control" 
                               id="country_id_display" 
                               value="<?= esc($set_country['name']) ?>" 
                               readonly>
                        <div class="form-text">Country cannot be changed after province creation</div>
                    </div>

                    <div class="mb-3">
                        <label for="json_id" class="form-label">JSON ID</label>
                        <input type="text" 
                               class="form-control" 
                               id="json_id" 
                               name="json_id" 
                               value="<?= old('json_id', $province['json_id']) ?>"
                               maxlength="50">
                        <div class="form-text">Optional JSON identifier for mapping purposes</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('dakoii/province/list') ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-warning text-dark">
                            <i class="fas fa-save"></i> Update Province
                        </button>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Information Panel -->
        <div class="col-md-4 col-lg-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Province Information</h5>
                </div>
                <div class="card-body">
                    <h6 class="fw-bold">Current Details</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>ID:</strong></td>
                            <td><?= esc($province['id']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td><?= date('M d, Y H:i', strtotime($province['created_at'])) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Last Updated:</strong></td>
                            <td><?= date('M d, Y H:i', strtotime($province['updated_at'])) ?></td>
                        </tr>
                    </table>
                    
                    <hr>
                    
                    <h6 class="fw-bold">Editing Guidelines</h6>
                    <p class="text-muted">When editing a province:</p>
                    <ul class="text-muted">
                        <li>Province code must remain unique across all provinces</li>
                        <li>Changing the province name will affect all related districts</li>
                        <li>Country assignment cannot be changed</li>
                        <li>JSON ID is used for external system integration</li>
                    </ul>

                    <hr>

                    <h6 class="fw-bold">Related Actions</h6>
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('dakoii/district/list/' . $province['id']) ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-list"></i> Manage Districts
                        </a>
                        <a href="<?= base_url('dakoii/province/delete/' . $province['id']) ?>" 
                           class="btn btn-outline-danger btn-sm"
                           onclick="return confirm('Are you sure you want to delete this province? This action cannot be undone and will affect all related districts.')">
                            <i class="fas fa-trash"></i> Delete Province
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
