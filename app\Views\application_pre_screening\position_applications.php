<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercises') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/positions') ?>">
                            <?= esc($exercise['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?> Applications
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-filter me-2"></i>Applications for Pre-Screening</h2>
            <p class="text-muted">
                Position: <strong><?= esc($position['designation']) ?></strong><br>
                <small>Exercise: <?= esc($exercise['exercise_name']) ?> | Group: <?= esc($positionGroup['group_name']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/positions') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Positions
            </a>
        </div>
    </div>

    <!-- Position Details Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-briefcase me-2"></i>Position Details
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Position Title:</strong><br>
                    <?= esc($position['designation']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Position Group:</strong><br>
                    <?= esc($positionGroup['group_name']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Exercise:</strong><br>
                    <?= esc($exercise['exercise_name']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Classification:</strong><br>
                    <span class="badge bg-secondary"><?= esc($position['classification'] ?? 'N/A') ?></span>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <strong>Location:</strong><br>
                    <i class="fas fa-map-marker-alt me-1"></i><?= esc($position['location'] ?? 'Not specified') ?>
                </div>
                <div class="col-md-3">
                    <strong>Salary Range:</strong><br>
                    <?= esc($position['annual_salary'] ?? 'Not specified') ?>
                </div>
                <div class="col-md-3">
                    <strong>Total Applications:</strong><br>
                    <span class="badge bg-primary fs-6"><?= count($applications) ?></span>
                </div>
                <div class="col-md-3">
                    <strong>Pending Pre-Screen:</strong><br>
                    <?php
                    $pending = count(array_filter($applications, function($app) {
                        return empty($app['pre_screened_status']);
                    }));
                    ?>
                    <span class="badge <?= $pending > 0 ? 'bg-warning' : 'bg-success' ?> fs-6"><?= $pending ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications List -->
    <div class="card">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Applications Requiring Pre-Screening
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No applications found for this position.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="applicationsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Application #</th>
                                <th>Applicant Name</th>
                                <th>Date Applied</th>
                                <th>Date Acknowledged</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($applications as $app): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td><?= esc($app['application_number']) ?></td>
                                    <td><?= esc($app['first_name'] . ' ' . $app['last_name']) ?></td>
                                    <td><?= date('d M Y', strtotime($app['created_at'])) ?></td>
                                    <td><?= date('d M Y', strtotime($app['recieved_acknowledged'])) ?></td>
                                    <td>
                                        <?php if (!empty($app['pre_screened_status'])): ?>
                                            <span class="badge <?=
                                                $app['pre_screened_status'] === 'passed' ? 'bg-success' :
                                                ($app['pre_screened_status'] === 'failed' ? 'bg-danger' : 'bg-warning')
                                            ?>">
                                                <?= ucfirst($app['pre_screened_status']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('application_pre_screening/show/' . $app['id']) ?>"
                                           class="btn btn-sm btn-primary"
                                           title="Pre-Screen Application">
                                            <i class="fas fa-clipboard-check me-1"></i> Pre-Screen
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Quick Statistics -->
                <div class="mt-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Application Statistics</h6>
                            <div class="row text-center">
                                <?php
                                $total = count($applications);
                                $passed = count(array_filter($applications, function($app) { return ($app['pre_screened_status'] ?? '') === 'passed'; }));
                                $failed = count(array_filter($applications, function($app) { return ($app['pre_screened_status'] ?? '') === 'failed'; }));
                                $pending = $total - $passed - $failed;
                                ?>
                                <div class="col-md-3">
                                    <h4 class="text-primary"><?= $total ?></h4>
                                    <small class="text-muted">Total Applications</small>
                                </div>
                                <div class="col-md-3">
                                    <h4 class="text-warning"><?= $pending ?></h4>
                                    <small class="text-muted">Pending</small>
                                </div>
                                <div class="col-md-3">
                                    <h4 class="text-success"><?= $passed ?></h4>
                                    <small class="text-muted">Passed</small>
                                </div>
                                <div class="col-md-3">
                                    <h4 class="text-danger"><?= $failed ?></h4>
                                    <small class="text-muted">Failed</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        responsive: true,
        order: [[3, 'desc']], // Sort by date applied desc
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications available for this position",
        }
    });

    console.log('Position Applications view loaded');
});
</script>
<?= $this->endSection() ?>